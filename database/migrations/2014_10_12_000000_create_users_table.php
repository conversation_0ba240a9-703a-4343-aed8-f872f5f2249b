<?php

use App\Models\Users\User;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('full_name', 100)->nullable();
            $table->integer('role_id');
            $table->unsignedBigInteger('parent_user_id')->default(0);
            $table->string('email', 100)->unique();
            $table->string('email_verification_method', 50)->nullable();
            $table->string('phone_number', 25);
            $table->string('phone_verification_method', 50)->nullable();
            $table->string('password');
            $table->string('referred_by', 100);
            $table->text('oauth_id')->nullable();
            $table->string('oauth_type', 50)->nullable();
            $table->rememberToken();
            $table->string('accessToken', 255)->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('phone_verified_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
