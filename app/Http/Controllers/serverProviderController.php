<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class serverProviderController extends Controller
{

   
    public function index()
    {
        $user = Auth::user();
        $roleId = $user->role_id; // Assuming 'role' is the column for user roles in the database

        // Role-based logic
        if ($roleId == 1) {
            // Superadmin
            return view('home.superadmin');
        } elseif ($roleId == 2) {
            // Admin
            return view('home.admin');
        } elseif ($roleId == 3) {
            // User/Jazz
            $provider = 'Jazz';
            return view('home.user', compact('provider'));
        } elseif ($roleId == 4) {
            // User/Zong
            $provider = 'Zong';
            return view('home.user', compact('provider'));
        } elseif ($roleId == 5) {
            // User/Ufone
            $provider = 'Ufone';
            return view('home.user', compact('provider'));
        } else {
            // Redirect unauthorized access to login or error page
            return redirect()->route('login')->with('error', 'Unauthorized access');
        }
    }
    
}
