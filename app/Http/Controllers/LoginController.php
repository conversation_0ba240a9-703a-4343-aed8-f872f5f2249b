<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LoginController extends Controller
{
    public function login(Request $request)
    {

        // dd($request->all());
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);
        
        $credentials = $request->only('email', 'password');


        // $user = DB::table('users')->get();
        // dd($user);

        if (Auth::attempt($credentials, $request->remember)) {
            // Redirect to the home page after login
            return redirect()->intended('/home');
        }

        // <PERSON><PERSON> failed, redirect back with error
        return back()->with('error', 'Invalid login credentials')->withInput();
    }

    public function logout(Request $request)
    {
        Auth::logout();
        return redirect('/'); // Redirect to login page
    }
}
