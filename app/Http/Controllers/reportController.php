<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class reportController extends Controller
{
    

    public function getFirst(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = DB::table('reporting_data')->select([
                    'date',
                    'new_subs',
                    'total_revenue',
                    'prepaid_revenue',
                    'postpaid_revenue',
                    'platform_base',
                    'total_unique_charged_subs',
                    'charging_success',
                    'operator',
                    'prepaid_charged',
                    'postpaid_charged',
                    'total_charged',
                    'unsubs'
                ]);
    
                // Apply operator filter if specified
                if ($request->has('operator') && $request->operator !== 'all') {
                    $query->where('operator', $request->operator);
                }
    
                // ✅ Apply date range filter if provided
                if ($request->filled('fromdate') && $request->filled('todate')) {
                    $query->whereBetween(DB::raw('DATE(date)'), [$request->fromdate, $request->todate]);
                }
    
                return DataTables::of($query)
                    ->addIndexColumn()
                    ->make(true);
            }
    
            // Fetch all distinct operators for filter dropdown
            $operators = DB::table('reporting_data')
                ->select('operator')
                ->distinct()
                ->get();
    
            return view('report.report', compact('operators'));
    
        } catch (\Exception $e) {
            \Log::error('Error fetching reporting data: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while fetching the data.'], 500);
        }
    }

    public function getZongReport(Request $request)
    {
        try {
            if ($request->ajax()) {
                $query = DB::table('zong_reporting')->select([
                    'report_date',
                    'channel',
                    'total_users_base',
                    'charged_base',
                    'uncharged_base',
                    'new_adds',
                    'rejoins',
                    'gross_adds',
                    'unsubs',
                    'purged_on_charging',
                    'total_churn',
                    'net_adds'
                ]);


                // ✅ Apply date range filter if provided
                if ($request->filled('fromdate') && $request->filled('todate')) {
                    $query->whereBetween(DB::raw('DATE(report_date)'), [$request->fromdate, $request->todate]);
                }

                return DataTables::of($query)
                    ->addIndexColumn()
                    ->make(true);
            }

            // Fetch all distinct channels for filter dropdown (if needed)
            $channels = DB::table('zong_reporting')
                ->select('channel')
                ->distinct()
                ->get();

            return view('report.zong', compact('channels'));

        } catch (\Exception $e) {
            \Log::error('Error fetching zong reporting data: ' . $e->getMessage());
            return response()->json(['error' => 'An error occurred while fetching the data.'], 500);
        }
    }
    




   
}

