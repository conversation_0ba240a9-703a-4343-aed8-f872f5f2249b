<?php

namespace App\Traits;

use App\Models\Users\User;
use Illuminate\Http\Request;
use App\Events\ValidateLogIn;
use App\Http\Requests\OtpRequest;
use Illuminate\Auth\Events\Logout;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

trait LoginUserNumber
{
    private $codeValidityTime = 10;

    /**
     * Revoke all the previous tokens of currently authenticated user
     *
     * @return void
     */
    protected function revokeTokens($user)
    {
        $tokens = $user->tokens()->get();
        if ($tokens) {
            foreach ($tokens as $token) {
                $token->revoke();
            }
        }
    }

    /**
     * return user of phone number
     * @param string $number
     * @param string $email
     * 
     * @return User|null
     */
    protected function user(string $number= null, string $email = null)
    {
        if (isset($number)) {
            return User::where('phone_number', $number)->first();
        }
        else {
            return User::where('email', $email)->first();
        }
    }

    /**
     * Get the failed login response instance.
     *
     * @param  OtpRequest  $request
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function sendFailedPhoneLoginResponse(OtpRequest $request)
    {     
        throw ValidationException::withMessages([
            'user_not_found' => [trans('auth.userNotFound')],
        ])->status(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Get the failed verification response instance.
     *
     * @param  Request  $request
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function sendFailedVerificationReasponse(Request $request)
    {
        throw ValidationException::withMessages([
            $this->username() => [trans('auth.failedVerification')],
        ])->status(Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Generate a random number
     *
     * @return int
     */
    protected function generateOtp()
    {
        return rand(100000, 999999);
    }

    /**
     * Store otp against phone number
     * 
     * @param string $number
     * @param int $otp
     * @return void
     */
    protected function storeOtp(string $number, int $otp)
    {
        cache([$number => $otp], now()->addMinutes(10));
    }

    protected function storeemailOtp(string $email,  $otp)
    {
      
        cache([$email => $otp['otp']], now()->addMinutes(10));
        cache(['user_email' => $email]);
        

  
    }

    /**
     * Send Otp to user
     * 
     * @param string $number
     * @param int $otp
     * 
     * @return bool
     */
    protected function dispatchOtp(string $number, int $otp)
    {
        $message = sprintf(
            'Your OTP for Jazz mosafir is %d. This otp is valid for %d minutes.',
            $otp,
            $this->codeValidityTime
        );
        $message = str_replace(" ", "__", $message);
        
        return Http::get('http://115.186.147.181:9133/api.php', [
            'action'  => 'sendsms',
            'mobile'  => $number,
            'message' => $message
        ]);
    }

    protected function dispatchemailOtp(string $email, $otp)
    {
    
        
        Mail::send('mail.forget_password_otp', $otp, function ($emailmessage) use ($email) {
            $emailmessage->to($email)->subject('Verification email');
          
        });

    }

    /**
     * Fire logout event.
     *
     * @param  string $guard
     * @param mixed $user
     * @return void
     */
    protected function fireLogOutEvent($guard, $user)
    {
        event(new Logout($guard, $user));
    }

    /**
     * Fire an event when a login occurs.
     *
     * @return void
     */
    protected function fireValidateLogInEvent()
    {
        event(new ValidateLogIn());
    }

    /**
     * Get the username property to be used by the controller.
     *
     * @return string
     */
    protected function username()
    {
        if (request()->path() === config('settings.login') || request()->path() === config('settings.forget_password') ) {
            return 'email';
        }
        return 'phone_number';
    }

    /**
     * Update password from md5 to bcrypt so evantually we have all users migrated to laravel native password encryption 
     * @param Request $request
     * 
     * @return void
     */
    protected function updatePasswordEncryption(Request $request): void
    {
        $user = User::where('email', $request->email)
            ->where('password', md5($request->password))
            ->first();
        $user->password = bcrypt($request->input('password'));

        $user->save();
    }

    /**
     * Login user using MD5 Encryption
     * @dev:: This function is to move users from md5 to bcrypt, we will remove
     * this method once all users moved to bcrypt passwords
     * @param Request $request
     * 
     * @return bool
     */
    protected function AttemptMd5Login(Request $request): bool
    {
        $isCredentialsValid = User::where('email', $request->email)
            ->where('password', md5($request->password))
            ->first();
        if ($isCredentialsValid) {
            return true;
        }

        return false;
    }
}
