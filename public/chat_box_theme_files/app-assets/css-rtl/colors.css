.bg-white .card-header, .bg-white .card-footer {
  background-color : transparent;
}

.border-white {
  border : 1px solid #FFFFFF !important;
}

.border-top-white {
  border-top : 1px solid #FFFFFF;
}

.border-bottom-white {
  border-bottom : 1px solid #FFFFFF;
}

.border-start-white {
  border-right : 1px solid #FFFFFF;
}

.border-end-white {
  border-left : 1px solid #FFFFFF;
}

.bg-white.badge-glow, .border-white.badge-glow {
  box-shadow : 0 0 10px #FFFFFF;
}

.overlay-white {
  background : #FFFFFF;
  /* The Fallback */
  background : rgba(255, 255, 255, 0.6);
}

input:focus ~ .bg-white {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #FFFFFF !important;
}

.bg-black .card-header, .bg-black .card-footer {
  background-color : transparent;
}

.border-black {
  border : 1px solid #000000 !important;
}

.border-top-black {
  border-top : 1px solid #000000;
}

.border-bottom-black {
  border-bottom : 1px solid #000000;
}

.border-start-black {
  border-right : 1px solid #000000;
}

.border-end-black {
  border-left : 1px solid #000000;
}

.bg-black.badge-glow, .border-black.badge-glow {
  box-shadow : 0 0 10px #000000;
}

.overlay-black {
  background : #000000;
  /* The Fallback */
  background : rgba(0, 0, 0, 0.6);
}

input:focus ~ .bg-black {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #000000 !important;
}

.bg-dark .card-header, .bg-dark .card-footer {
  background-color : transparent;
}

.alert-dark {
  background : rgba(75, 75, 75, 0.12) !important;
  color : #4B4B4B !important;
}

.alert-dark .alert-heading {
  box-shadow : rgba(75, 75, 75, 0.4) 0 6px 15px -7px;
}

.alert-dark .alert-link {
  color : #3E3E3E !important;
}

.alert-dark .btn-close {
  background : transparent url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%234b4b4b\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat;
  color : #4B4B4B !important;
}

.border-dark {
  border : 1px solid #4B4B4B !important;
}

.border-top-dark {
  border-top : 1px solid #4B4B4B;
}

.border-bottom-dark {
  border-bottom : 1px solid #4B4B4B;
}

.border-start-dark {
  border-right : 1px solid #4B4B4B;
}

.border-end-dark {
  border-left : 1px solid #4B4B4B;
}

.bg-dark.badge-glow, .border-dark.badge-glow {
  box-shadow : 0 0 10px #4B4B4B;
}

.badge.badge-light-dark {
  background-color : rgba(75, 75, 75, 0.12);
  color : #4B4B4B !important;
}

.overlay-dark {
  background : #4B4B4B;
  /* The Fallback */
  background : rgba(75, 75, 75, 0.6);
}

.btn-dark {
  border-color : #4B4B4B !important;
  background-color : #4B4B4B !important;
  color : #FFFFFF !important;
}

.btn-dark:focus, .btn-dark:active, .btn-dark.active {
  color : #FFFFFF;
  background-color : #343434 !important;
}

.btn-dark:hover:not(.disabled):not(:disabled) {
  box-shadow : 0 8px 25px -8px #4B4B4B;
}

.btn-dark:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-check:checked + .btn-dark, .btn-check:active + .btn-dark {
  color : #FFFFFF;
  background-color : #343434 !important;
}

.btn-flat-dark {
  background-color : transparent;
  color : #4B4B4B;
}

.btn-flat-dark:hover {
  color : #4B4B4B;
}

.btn-flat-dark:hover:not(.disabled):not(:disabled) {
  background-color : rgba(75, 75, 75, 0.12);
}

.btn-flat-dark:active, .btn-flat-dark.active, .btn-flat-dark:focus {
  background-color : rgba(75, 75, 75, 0.2);
  color : #4B4B4B;
}

.btn-flat-dark.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%234b4b4b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-relief-dark {
  background-color : #4B4B4B;
  box-shadow : inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
}

.btn-relief-dark:hover:not(.disabled):not(:disabled) {
  background-color : #626262;
}

.btn-relief-dark:active, .btn-relief-dark.active, .btn-relief-dark:focus {
  background-color : #343434;
}

.btn-relief-dark:hover {
  color : #FFFFFF;
}

.btn-relief-dark:active, .btn-relief-dark.active {
  outline : none;
  box-shadow : none;
  -webkit-transform : translateY(3px);
      -ms-transform : translateY(3px);
          transform : translateY(3px);
}

.btn-outline-dark {
  border : 1px solid #4B4B4B !important;
  background-color : transparent;
  color : #4B4B4B;
}

.btn-outline-dark:hover:not(.disabled):not(:disabled) {
  background-color : rgba(75, 75, 75, 0.04);
  color : #4B4B4B;
}

.btn-outline-dark:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-outline-dark:not(:disabled):not(.disabled):active, .btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):focus {
  background-color : rgba(75, 75, 75, 0.2);
  color : #4B4B4B;
}

.btn-outline-dark.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%234b4b4b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-outline-dark.show.dropdown-toggle {
  background-color : rgba(75, 75, 75, 0.2);
  color : #4B4B4B;
}

.btn-check:checked + .btn-outline-dark, .btn-check:active + .btn-outline-dark {
  color : #4B4B4B;
  background-color : rgba(75, 75, 75, 0.2) !important;
}

.btn-outline-dark.waves-effect .waves-ripple, .btn-flat-dark.waves-effect .waves-ripple {
  background : -webkit-radial-gradient(rgba(75, 75, 75, 0.2) 0, rgba(75, 75, 75, 0.3) 40%, rgba(75, 75, 75, 0.4) 50%, rgba(75, 75, 75, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background :         radial-gradient(rgba(75, 75, 75, 0.2) 0, rgba(75, 75, 75, 0.3) 40%, rgba(75, 75, 75, 0.4) 50%, rgba(75, 75, 75, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.bullet.bullet-dark {
  background-color : #4B4B4B;
}

.modal.modal-dark .modal-header .modal-title {
  color : #4B4B4B;
}

.modal.modal-dark .modal-header .btn-close {
  background : #FFFFFF url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%234b4b4b\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat !important;
  color : #4B4B4B !important;
}

.progress-bar-dark {
  background-color : rgba(75, 75, 75, 0.12);
}

.progress-bar-dark .progress-bar {
  background-color : #4B4B4B;
}

.timeline .timeline-point-dark {
  border-color : #4B4B4B !important;
}

.timeline .timeline-point-dark i, .timeline .timeline-point-dark svg {
  stroke : #4B4B4B !important;
}

.timeline .timeline-point-dark.timeline-point-indicator {
  background-color : #4B4B4B !important;
}

.timeline .timeline-point-dark.timeline-point-indicator:before {
  background : rgba(75, 75, 75, 0.12) !important;
}

.divider.divider-dark .divider-text:before, .divider.divider-dark .divider-text:after {
  border-color : #4B4B4B !important;
}

input:focus ~ .bg-dark {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #4B4B4B !important;
}

.form-check-dark .form-check-input:checked {
  border-color : #4B4B4B;
  background-color : #4B4B4B;
}

.form-check-dark .form-check-input:not(:disabled):checked, .form-check-dark .form-check-input:not(:disabled):focus {
  border-color : #4B4B4B;
  box-shadow : 0 2px 4px 0 rgba(75, 75, 75, 0.4);
}

.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background : #4B4B4B !important;
  border-color : #4B4B4B !important;
}

.bg-light .card-header, .bg-light .card-footer {
  background-color : transparent;
}

.border-light {
  border : 1px solid #F6F6F6 !important;
}

.border-top-light {
  border-top : 1px solid #F6F6F6;
}

.border-bottom-light {
  border-bottom : 1px solid #F6F6F6;
}

.border-start-light {
  border-right : 1px solid #F6F6F6;
}

.border-end-light {
  border-left : 1px solid #F6F6F6;
}

.bg-light.badge-glow, .border-light.badge-glow {
  box-shadow : 0 0 10px #F6F6F6;
}

.overlay-light {
  background : #F6F6F6;
  /* The Fallback */
  background : rgba(246, 246, 246, 0.6);
}

input:focus ~ .bg-light {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #F6F6F6 !important;
}

.bg-primary .card-header, .bg-primary .card-footer {
  background-color : transparent;
}

.alert-primary {
  background : rgba(115, 103, 240, 0.12) !important;
  color : #7367F0 !important;
}

.alert-primary .alert-heading {
  box-shadow : rgba(115, 103, 240, 0.4) 0 6px 15px -7px;
}

.alert-primary .alert-link {
  color : #5E50EE !important;
}

.alert-primary .btn-close {
  background : transparent url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%237367f0\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat;
  color : #7367F0 !important;
}

.bg-light-primary {
  background : rgba(115, 103, 240, 0.12) !important;
  color : #7367F0 !important;
}

.bg-light-primary.fc-h-event, .bg-light-primary.fc-v-event {
  border-color : rgba(115, 103, 240, 0.1);
}

.bg-light-primary .fc-list-event-dot {
  border-color : #7367F0 !important;
}

.bg-light-primary.fc-list-event:hover td {
  background : rgba(115, 103, 240, 0.1) !important;
}

.bg-light-primary.fc-list-event .fc-list-event-title {
  color : #6E6B7B;
}

.avatar.bg-light-primary {
  color : #7367F0 !important;
}

.border-primary {
  border : 1px solid #7367F0 !important;
}

.border-top-primary {
  border-top : 1px solid #7367F0;
}

.border-bottom-primary {
  border-bottom : 1px solid #7367F0;
}

.border-start-primary {
  border-right : 1px solid #7367F0;
}

.border-end-primary {
  border-left : 1px solid #7367F0;
}

.bg-primary.badge-glow, .border-primary.badge-glow {
  box-shadow : 0 0 10px #7367F0;
}

.badge.badge-light-primary {
  background-color : rgba(115, 103, 240, 0.12);
  color : #7367F0 !important;
}

.overlay-primary {
  background : #7367F0;
  /* The Fallback */
  background : rgba(115, 103, 240, 0.6);
}

.btn-primary {
  border-color : #7367F0 !important;
  background-color : #7367F0 !important;
  color : #FFFFFF !important;
}

.btn-primary:focus, .btn-primary:active, .btn-primary.active {
  color : #FFFFFF;
  background-color : #5E50EE !important;
}

.btn-primary:hover:not(.disabled):not(:disabled) {
  box-shadow : 0 8px 25px -8px #7367F0;
}

.btn-primary:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-check:checked + .btn-primary, .btn-check:active + .btn-primary {
  color : #FFFFFF;
  background-color : #5E50EE !important;
}

.btn-flat-primary {
  background-color : transparent;
  color : #7367F0;
}

.btn-flat-primary:hover {
  color : #7367F0;
}

.btn-flat-primary:hover:not(.disabled):not(:disabled) {
  background-color : rgba(115, 103, 240, 0.12);
}

.btn-flat-primary:active, .btn-flat-primary.active, .btn-flat-primary:focus {
  background-color : rgba(115, 103, 240, 0.2);
  color : #7367F0;
}

.btn-flat-primary.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%237367f0\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-relief-primary {
  background-color : #7367F0;
  box-shadow : inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
}

.btn-relief-primary:hover:not(.disabled):not(:disabled) {
  background-color : #887EF2;
}

.btn-relief-primary:active, .btn-relief-primary.active, .btn-relief-primary:focus {
  background-color : #5E50EE;
}

.btn-relief-primary:hover {
  color : #FFFFFF;
}

.btn-relief-primary:active, .btn-relief-primary.active {
  outline : none;
  box-shadow : none;
  -webkit-transform : translateY(3px);
      -ms-transform : translateY(3px);
          transform : translateY(3px);
}

.btn-outline-primary {
  border : 1px solid #7367F0 !important;
  background-color : transparent;
  color : #7367F0;
}

.btn-outline-primary:hover:not(.disabled):not(:disabled) {
  background-color : rgba(115, 103, 240, 0.04);
  color : #7367F0;
}

.btn-outline-primary:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):focus {
  background-color : rgba(115, 103, 240, 0.2);
  color : #7367F0;
}

.btn-outline-primary.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%237367f0\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-outline-primary.show.dropdown-toggle {
  background-color : rgba(115, 103, 240, 0.2);
  color : #7367F0;
}

.btn-check:checked + .btn-outline-primary, .btn-check:active + .btn-outline-primary {
  color : #7367F0;
  background-color : rgba(115, 103, 240, 0.2) !important;
}

.btn-outline-primary.waves-effect .waves-ripple, .btn-flat-primary.waves-effect .waves-ripple {
  background : -webkit-radial-gradient(rgba(115, 103, 240, 0.2) 0, rgba(115, 103, 240, 0.3) 40%, rgba(115, 103, 240, 0.4) 50%, rgba(115, 103, 240, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background :         radial-gradient(rgba(115, 103, 240, 0.2) 0, rgba(115, 103, 240, 0.3) 40%, rgba(115, 103, 240, 0.4) 50%, rgba(115, 103, 240, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.bullet.bullet-primary {
  background-color : #7367F0;
}

.modal.modal-primary .modal-header .modal-title {
  color : #7367F0;
}

.modal.modal-primary .modal-header .btn-close {
  background : #FFFFFF url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%237367f0\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat !important;
  color : #7367F0 !important;
}

.pagination-primary .page-item.active .page-link {
  background : #7367F0 !important;
  color : #FFFFFF;
}

.pagination-primary .page-item.active .page-link:hover {
  color : #FFFFFF;
}

.pagination-primary .page-item .page-link:hover {
  color : #7367F0;
}

.pagination-primary .page-item.prev-item .page-link:hover, .pagination-primary .page-item.next-item .page-link:hover {
  background : #7367F0;
  color : #FFFFFF;
}

.pagination-primary .page-item.next-item .page-link:active:after, .pagination-primary .page-item.next-item .page-link:hover:after, .pagination-primary .page-item.next .page-link:active:after, .pagination-primary .page-item.next .page-link:hover:after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%237367f0\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.pagination-primary .page-item.prev-item .page-link:active:before, .pagination-primary .page-item.prev-item .page-link:hover:before, .pagination-primary .page-item.prev .page-link:active:before, .pagination-primary .page-item.prev .page-link:hover:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%237367f0\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-left\'%3E%3Cpolyline points=\'15 18 9 12 15 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.nav-pill-primary .nav-item .nav-link.active {
  color : #FFFFFF;
  background-color : #7367F0 !important;
  border-color : #7367F0;
  box-shadow : 0 4px 18px -4px rgba(115, 103, 240, 0.65);
}

.progress-bar-primary {
  background-color : rgba(115, 103, 240, 0.12);
}

.progress-bar-primary .progress-bar {
  background-color : #7367F0;
}

.timeline .timeline-point-primary {
  border-color : #7367F0 !important;
}

.timeline .timeline-point-primary i, .timeline .timeline-point-primary svg {
  stroke : #7367F0 !important;
}

.timeline .timeline-point-primary.timeline-point-indicator {
  background-color : #7367F0 !important;
}

.timeline .timeline-point-primary.timeline-point-indicator:before {
  background : rgba(115, 103, 240, 0.12) !important;
}

.divider.divider-primary .divider-text:before, .divider.divider-primary .divider-text:after {
  border-color : #7367F0 !important;
}

input:focus ~ .bg-primary {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #7367F0 !important;
}

.form-check-primary .form-check-input:checked {
  border-color : #7367F0;
  background-color : #7367F0;
}

.form-check-primary .form-check-input:not(:disabled):checked, .form-check-primary .form-check-input:not(:disabled):focus {
  border-color : #7367F0;
  box-shadow : 0 2px 4px 0 rgba(115, 103, 240, 0.4);
}

.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background : #7367F0 !important;
  border-color : #7367F0 !important;
}

.bg-secondary .card-header, .bg-secondary .card-footer {
  background-color : transparent;
}

.alert-secondary {
  background : rgba(130, 134, 139, 0.12) !important;
  color : #82868B !important;
}

.alert-secondary .alert-heading {
  box-shadow : rgba(130, 134, 139, 0.4) 0 6px 15px -7px;
}

.alert-secondary .alert-link {
  color : #75797E !important;
}

.alert-secondary .btn-close {
  background : transparent url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%2382868b\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat;
  color : #82868B !important;
}

.bg-light-secondary {
  background : rgba(130, 134, 139, 0.12) !important;
  color : #82868B !important;
}

.bg-light-secondary.fc-h-event, .bg-light-secondary.fc-v-event {
  border-color : rgba(130, 134, 139, 0.1);
}

.bg-light-secondary .fc-list-event-dot {
  border-color : #82868B !important;
}

.bg-light-secondary.fc-list-event:hover td {
  background : rgba(130, 134, 139, 0.1) !important;
}

.bg-light-secondary.fc-list-event .fc-list-event-title {
  color : #6E6B7B;
}

.avatar.bg-light-secondary {
  color : #82868B !important;
}

.border-secondary {
  border : 1px solid #82868B !important;
}

.border-top-secondary {
  border-top : 1px solid #82868B;
}

.border-bottom-secondary {
  border-bottom : 1px solid #82868B;
}

.border-start-secondary {
  border-right : 1px solid #82868B;
}

.border-end-secondary {
  border-left : 1px solid #82868B;
}

.bg-secondary.badge-glow, .border-secondary.badge-glow {
  box-shadow : 0 0 10px #82868B;
}

.badge.badge-light-secondary {
  background-color : rgba(130, 134, 139, 0.12);
  color : #82868B !important;
}

.overlay-secondary {
  background : #82868B;
  /* The Fallback */
  background : rgba(130, 134, 139, 0.6);
}

.btn-secondary {
  border-color : #82868B !important;
  background-color : #82868B !important;
  color : #FFFFFF !important;
}

.btn-secondary:focus, .btn-secondary:active, .btn-secondary.active {
  color : #FFFFFF;
  background-color : #75797E !important;
}

.btn-secondary:hover:not(.disabled):not(:disabled) {
  box-shadow : 0 8px 25px -8px #82868B;
}

.btn-secondary:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-check:checked + .btn-secondary, .btn-check:active + .btn-secondary {
  color : #FFFFFF;
  background-color : #75797E !important;
}

.btn-flat-secondary {
  background-color : transparent;
  color : #82868B;
}

.btn-flat-secondary:hover {
  color : #82868B;
}

.btn-flat-secondary:hover:not(.disabled):not(:disabled) {
  background-color : rgba(130, 134, 139, 0.12);
}

.btn-flat-secondary:active, .btn-flat-secondary.active, .btn-flat-secondary:focus {
  background-color : rgba(130, 134, 139, 0.2);
  color : #82868B;
}

.btn-flat-secondary.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2382868b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-relief-secondary {
  background-color : #82868B;
  box-shadow : inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
}

.btn-relief-secondary:hover:not(.disabled):not(:disabled) {
  background-color : #8F9397;
}

.btn-relief-secondary:active, .btn-relief-secondary.active, .btn-relief-secondary:focus {
  background-color : #75797E;
}

.btn-relief-secondary:hover {
  color : #FFFFFF;
}

.btn-relief-secondary:active, .btn-relief-secondary.active {
  outline : none;
  box-shadow : none;
  -webkit-transform : translateY(3px);
      -ms-transform : translateY(3px);
          transform : translateY(3px);
}

.btn-outline-secondary {
  border : 1px solid #82868B !important;
  background-color : transparent;
  color : #82868B;
}

.btn-outline-secondary:hover:not(.disabled):not(:disabled) {
  background-color : rgba(130, 134, 139, 0.04);
  color : #82868B;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-outline-secondary:not(:disabled):not(.disabled):active, .btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):focus {
  background-color : rgba(130, 134, 139, 0.2);
  color : #82868B;
}

.btn-outline-secondary.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2382868b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-outline-secondary.show.dropdown-toggle {
  background-color : rgba(130, 134, 139, 0.2);
  color : #82868B;
}

.btn-check:checked + .btn-outline-secondary, .btn-check:active + .btn-outline-secondary {
  color : #82868B;
  background-color : rgba(130, 134, 139, 0.2) !important;
}

.btn-outline-secondary.waves-effect .waves-ripple, .btn-flat-secondary.waves-effect .waves-ripple {
  background : -webkit-radial-gradient(rgba(130, 134, 139, 0.2) 0, rgba(130, 134, 139, 0.3) 40%, rgba(130, 134, 139, 0.4) 50%, rgba(130, 134, 139, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background :         radial-gradient(rgba(130, 134, 139, 0.2) 0, rgba(130, 134, 139, 0.3) 40%, rgba(130, 134, 139, 0.4) 50%, rgba(130, 134, 139, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.bullet.bullet-secondary {
  background-color : #82868B;
}

.modal.modal-secondary .modal-header .modal-title {
  color : #82868B;
}

.modal.modal-secondary .modal-header .btn-close {
  background : #FFFFFF url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%2382868b\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat !important;
  color : #82868B !important;
}

.pagination-secondary .page-item.active .page-link {
  background : #82868B !important;
  color : #FFFFFF;
}

.pagination-secondary .page-item.active .page-link:hover {
  color : #FFFFFF;
}

.pagination-secondary .page-item .page-link:hover {
  color : #82868B;
}

.pagination-secondary .page-item.prev-item .page-link:hover, .pagination-secondary .page-item.next-item .page-link:hover {
  background : #82868B;
  color : #FFFFFF;
}

.pagination-secondary .page-item.next-item .page-link:active:after, .pagination-secondary .page-item.next-item .page-link:hover:after, .pagination-secondary .page-item.next .page-link:active:after, .pagination-secondary .page-item.next .page-link:hover:after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2382868b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.pagination-secondary .page-item.prev-item .page-link:active:before, .pagination-secondary .page-item.prev-item .page-link:hover:before, .pagination-secondary .page-item.prev .page-link:active:before, .pagination-secondary .page-item.prev .page-link:hover:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2382868b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-left\'%3E%3Cpolyline points=\'15 18 9 12 15 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.nav-pill-secondary .nav-item .nav-link.active {
  color : #FFFFFF;
  background-color : #82868B !important;
  border-color : #82868B;
  box-shadow : 0 4px 18px -4px rgba(130, 134, 139, 0.65);
}

.progress-bar-secondary {
  background-color : rgba(130, 134, 139, 0.12);
}

.progress-bar-secondary .progress-bar {
  background-color : #82868B;
}

.timeline .timeline-point-secondary {
  border-color : #82868B !important;
}

.timeline .timeline-point-secondary i, .timeline .timeline-point-secondary svg {
  stroke : #82868B !important;
}

.timeline .timeline-point-secondary.timeline-point-indicator {
  background-color : #82868B !important;
}

.timeline .timeline-point-secondary.timeline-point-indicator:before {
  background : rgba(130, 134, 139, 0.12) !important;
}

.divider.divider-secondary .divider-text:before, .divider.divider-secondary .divider-text:after {
  border-color : #82868B !important;
}

input:focus ~ .bg-secondary {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #82868B !important;
}

.form-check-secondary .form-check-input:checked {
  border-color : #82868B;
  background-color : #82868B;
}

.form-check-secondary .form-check-input:not(:disabled):checked, .form-check-secondary .form-check-input:not(:disabled):focus {
  border-color : #82868B;
  box-shadow : 0 2px 4px 0 rgba(130, 134, 139, 0.4);
}

.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background : #82868B !important;
  border-color : #82868B !important;
}

.bg-success .card-header, .bg-success .card-footer {
  background-color : transparent;
}

.alert-success {
  background : rgba(40, 199, 111, 0.12) !important;
  color : #28C76F !important;
}

.alert-success .alert-heading {
  box-shadow : rgba(40, 199, 111, 0.4) 0 6px 15px -7px;
}

.alert-success .alert-link {
  color : #24B263 !important;
}

.alert-success .btn-close {
  background : transparent url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%2328c76f\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat;
  color : #28C76F !important;
}

.bg-light-success {
  background : rgba(40, 199, 111, 0.12) !important;
  color : #28C76F !important;
}

.bg-light-success.fc-h-event, .bg-light-success.fc-v-event {
  border-color : rgba(40, 199, 111, 0.1);
}

.bg-light-success .fc-list-event-dot {
  border-color : #28C76F !important;
}

.bg-light-success.fc-list-event:hover td {
  background : rgba(40, 199, 111, 0.1) !important;
}

.bg-light-success.fc-list-event .fc-list-event-title {
  color : #6E6B7B;
}

.avatar.bg-light-success {
  color : #28C76F !important;
}

.border-success {
  border : 1px solid #28C76F !important;
}

.border-top-success {
  border-top : 1px solid #28C76F;
}

.border-bottom-success {
  border-bottom : 1px solid #28C76F;
}

.border-start-success {
  border-right : 1px solid #28C76F;
}

.border-end-success {
  border-left : 1px solid #28C76F;
}

.bg-success.badge-glow, .border-success.badge-glow {
  box-shadow : 0 0 10px #28C76F;
}

.badge.badge-light-success {
  background-color : rgba(40, 199, 111, 0.12);
  color : #28C76F !important;
}

.overlay-success {
  background : #28C76F;
  /* The Fallback */
  background : rgba(40, 199, 111, 0.6);
}

.btn-success {
  border-color : #28C76F !important;
  background-color : #28C76F !important;
  color : #FFFFFF !important;
}

.btn-success:focus, .btn-success:active, .btn-success.active {
  color : #FFFFFF;
  background-color : #24B263 !important;
}

.btn-success:hover:not(.disabled):not(:disabled) {
  box-shadow : 0 8px 25px -8px #28C76F;
}

.btn-success:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-check:checked + .btn-success, .btn-check:active + .btn-success {
  color : #FFFFFF;
  background-color : #24B263 !important;
}

.btn-flat-success {
  background-color : transparent;
  color : #28C76F;
}

.btn-flat-success:hover {
  color : #28C76F;
}

.btn-flat-success:hover:not(.disabled):not(:disabled) {
  background-color : rgba(40, 199, 111, 0.12);
}

.btn-flat-success:active, .btn-flat-success.active, .btn-flat-success:focus {
  background-color : rgba(40, 199, 111, 0.2);
  color : #28C76F;
}

.btn-flat-success.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2328c76f\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-relief-success {
  background-color : #28C76F;
  box-shadow : inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
}

.btn-relief-success:hover:not(.disabled):not(:disabled) {
  background-color : #33D67C;
}

.btn-relief-success:active, .btn-relief-success.active, .btn-relief-success:focus {
  background-color : #24B263;
}

.btn-relief-success:hover {
  color : #FFFFFF;
}

.btn-relief-success:active, .btn-relief-success.active {
  outline : none;
  box-shadow : none;
  -webkit-transform : translateY(3px);
      -ms-transform : translateY(3px);
          transform : translateY(3px);
}

.btn-outline-success {
  border : 1px solid #28C76F !important;
  background-color : transparent;
  color : #28C76F;
}

.btn-outline-success:hover:not(.disabled):not(:disabled) {
  background-color : rgba(40, 199, 111, 0.04);
  color : #28C76F;
}

.btn-outline-success:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-outline-success:not(:disabled):not(.disabled):active, .btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):focus {
  background-color : rgba(40, 199, 111, 0.2);
  color : #28C76F;
}

.btn-outline-success.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2328c76f\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-outline-success.show.dropdown-toggle {
  background-color : rgba(40, 199, 111, 0.2);
  color : #28C76F;
}

.btn-check:checked + .btn-outline-success, .btn-check:active + .btn-outline-success {
  color : #28C76F;
  background-color : rgba(40, 199, 111, 0.2) !important;
}

.btn-outline-success.waves-effect .waves-ripple, .btn-flat-success.waves-effect .waves-ripple {
  background : -webkit-radial-gradient(rgba(40, 199, 111, 0.2) 0, rgba(40, 199, 111, 0.3) 40%, rgba(40, 199, 111, 0.4) 50%, rgba(40, 199, 111, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background :         radial-gradient(rgba(40, 199, 111, 0.2) 0, rgba(40, 199, 111, 0.3) 40%, rgba(40, 199, 111, 0.4) 50%, rgba(40, 199, 111, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.bullet.bullet-success {
  background-color : #28C76F;
}

.modal.modal-success .modal-header .modal-title {
  color : #28C76F;
}

.modal.modal-success .modal-header .btn-close {
  background : #FFFFFF url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%2328c76f\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat !important;
  color : #28C76F !important;
}

.pagination-success .page-item.active .page-link {
  background : #28C76F !important;
  color : #FFFFFF;
}

.pagination-success .page-item.active .page-link:hover {
  color : #FFFFFF;
}

.pagination-success .page-item .page-link:hover {
  color : #28C76F;
}

.pagination-success .page-item.prev-item .page-link:hover, .pagination-success .page-item.next-item .page-link:hover {
  background : #28C76F;
  color : #FFFFFF;
}

.pagination-success .page-item.next-item .page-link:active:after, .pagination-success .page-item.next-item .page-link:hover:after, .pagination-success .page-item.next .page-link:active:after, .pagination-success .page-item.next .page-link:hover:after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2328c76f\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.pagination-success .page-item.prev-item .page-link:active:before, .pagination-success .page-item.prev-item .page-link:hover:before, .pagination-success .page-item.prev .page-link:active:before, .pagination-success .page-item.prev .page-link:hover:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2328c76f\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-left\'%3E%3Cpolyline points=\'15 18 9 12 15 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.nav-pill-success .nav-item .nav-link.active {
  color : #FFFFFF;
  background-color : #28C76F !important;
  border-color : #28C76F;
  box-shadow : 0 4px 18px -4px rgba(40, 199, 111, 0.65);
}

.progress-bar-success {
  background-color : rgba(40, 199, 111, 0.12);
}

.progress-bar-success .progress-bar {
  background-color : #28C76F;
}

.timeline .timeline-point-success {
  border-color : #28C76F !important;
}

.timeline .timeline-point-success i, .timeline .timeline-point-success svg {
  stroke : #28C76F !important;
}

.timeline .timeline-point-success.timeline-point-indicator {
  background-color : #28C76F !important;
}

.timeline .timeline-point-success.timeline-point-indicator:before {
  background : rgba(40, 199, 111, 0.12) !important;
}

.divider.divider-success .divider-text:before, .divider.divider-success .divider-text:after {
  border-color : #28C76F !important;
}

input:focus ~ .bg-success {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #28C76F !important;
}

.form-check-success .form-check-input:checked {
  border-color : #28C76F;
  background-color : #28C76F;
}

.form-check-success .form-check-input:not(:disabled):checked, .form-check-success .form-check-input:not(:disabled):focus {
  border-color : #28C76F;
  box-shadow : 0 2px 4px 0 rgba(40, 199, 111, 0.4);
}

.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background : #28C76F !important;
  border-color : #28C76F !important;
}

.bg-info .card-header, .bg-info .card-footer {
  background-color : transparent;
}

.alert-info {
  background : rgba(0, 207, 232, 0.12) !important;
  color : #00CFE8 !important;
}

.alert-info .alert-heading {
  box-shadow : rgba(0, 207, 232, 0.4) 0 6px 15px -7px;
}

.alert-info .alert-link {
  color : #00B8CF !important;
}

.alert-info .btn-close {
  background : transparent url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%2300cfe8\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat;
  color : #00CFE8 !important;
}

.bg-light-info {
  background : rgba(0, 207, 232, 0.12) !important;
  color : #00CFE8 !important;
}

.bg-light-info.fc-h-event, .bg-light-info.fc-v-event {
  border-color : rgba(0, 207, 232, 0.1);
}

.bg-light-info .fc-list-event-dot {
  border-color : #00CFE8 !important;
}

.bg-light-info.fc-list-event:hover td {
  background : rgba(0, 207, 232, 0.1) !important;
}

.bg-light-info.fc-list-event .fc-list-event-title {
  color : #6E6B7B;
}

.avatar.bg-light-info {
  color : #00CFE8 !important;
}

.border-info {
  border : 1px solid #00CFE8 !important;
}

.border-top-info {
  border-top : 1px solid #00CFE8;
}

.border-bottom-info {
  border-bottom : 1px solid #00CFE8;
}

.border-start-info {
  border-right : 1px solid #00CFE8;
}

.border-end-info {
  border-left : 1px solid #00CFE8;
}

.bg-info.badge-glow, .border-info.badge-glow {
  box-shadow : 0 0 10px #00CFE8;
}

.badge.badge-light-info {
  background-color : rgba(0, 207, 232, 0.12);
  color : #00CFE8 !important;
}

.overlay-info {
  background : #00CFE8;
  /* The Fallback */
  background : rgba(0, 207, 232, 0.6);
}

.btn-info {
  border-color : #00CFE8 !important;
  background-color : #00CFE8 !important;
  color : #FFFFFF !important;
}

.btn-info:focus, .btn-info:active, .btn-info.active {
  color : #FFFFFF;
  background-color : #00B8CF !important;
}

.btn-info:hover:not(.disabled):not(:disabled) {
  box-shadow : 0 8px 25px -8px #00CFE8;
}

.btn-info:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-check:checked + .btn-info, .btn-check:active + .btn-info {
  color : #FFFFFF;
  background-color : #00B8CF !important;
}

.btn-flat-info {
  background-color : transparent;
  color : #00CFE8;
}

.btn-flat-info:hover {
  color : #00CFE8;
}

.btn-flat-info:hover:not(.disabled):not(:disabled) {
  background-color : rgba(0, 207, 232, 0.12);
}

.btn-flat-info:active, .btn-flat-info.active, .btn-flat-info:focus {
  background-color : rgba(0, 207, 232, 0.2);
  color : #00CFE8;
}

.btn-flat-info.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2300cfe8\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-relief-info {
  background-color : #00CFE8;
  box-shadow : inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
}

.btn-relief-info:hover:not(.disabled):not(:disabled) {
  background-color : #03E4FF;
}

.btn-relief-info:active, .btn-relief-info.active, .btn-relief-info:focus {
  background-color : #00B8CF;
}

.btn-relief-info:hover {
  color : #FFFFFF;
}

.btn-relief-info:active, .btn-relief-info.active {
  outline : none;
  box-shadow : none;
  -webkit-transform : translateY(3px);
      -ms-transform : translateY(3px);
          transform : translateY(3px);
}

.btn-outline-info {
  border : 1px solid #00CFE8 !important;
  background-color : transparent;
  color : #00CFE8;
}

.btn-outline-info:hover:not(.disabled):not(:disabled) {
  background-color : rgba(0, 207, 232, 0.04);
  color : #00CFE8;
}

.btn-outline-info:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-outline-info:not(:disabled):not(.disabled):active, .btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):focus {
  background-color : rgba(0, 207, 232, 0.2);
  color : #00CFE8;
}

.btn-outline-info.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2300cfe8\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-outline-info.show.dropdown-toggle {
  background-color : rgba(0, 207, 232, 0.2);
  color : #00CFE8;
}

.btn-check:checked + .btn-outline-info, .btn-check:active + .btn-outline-info {
  color : #00CFE8;
  background-color : rgba(0, 207, 232, 0.2) !important;
}

.btn-outline-info.waves-effect .waves-ripple, .btn-flat-info.waves-effect .waves-ripple {
  background : -webkit-radial-gradient(rgba(0, 207, 232, 0.2) 0, rgba(0, 207, 232, 0.3) 40%, rgba(0, 207, 232, 0.4) 50%, rgba(0, 207, 232, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background :         radial-gradient(rgba(0, 207, 232, 0.2) 0, rgba(0, 207, 232, 0.3) 40%, rgba(0, 207, 232, 0.4) 50%, rgba(0, 207, 232, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.bullet.bullet-info {
  background-color : #00CFE8;
}

.modal.modal-info .modal-header .modal-title {
  color : #00CFE8;
}

.modal.modal-info .modal-header .btn-close {
  background : #FFFFFF url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%2300cfe8\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat !important;
  color : #00CFE8 !important;
}

.pagination-info .page-item.active .page-link {
  background : #00CFE8 !important;
  color : #FFFFFF;
}

.pagination-info .page-item.active .page-link:hover {
  color : #FFFFFF;
}

.pagination-info .page-item .page-link:hover {
  color : #00CFE8;
}

.pagination-info .page-item.prev-item .page-link:hover, .pagination-info .page-item.next-item .page-link:hover {
  background : #00CFE8;
  color : #FFFFFF;
}

.pagination-info .page-item.next-item .page-link:active:after, .pagination-info .page-item.next-item .page-link:hover:after, .pagination-info .page-item.next .page-link:active:after, .pagination-info .page-item.next .page-link:hover:after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2300cfe8\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.pagination-info .page-item.prev-item .page-link:active:before, .pagination-info .page-item.prev-item .page-link:hover:before, .pagination-info .page-item.prev .page-link:active:before, .pagination-info .page-item.prev .page-link:hover:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%2300cfe8\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-left\'%3E%3Cpolyline points=\'15 18 9 12 15 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.nav-pill-info .nav-item .nav-link.active {
  color : #FFFFFF;
  background-color : #00CFE8 !important;
  border-color : #00CFE8;
  box-shadow : 0 4px 18px -4px rgba(0, 207, 232, 0.65);
}

.progress-bar-info {
  background-color : rgba(0, 207, 232, 0.12);
}

.progress-bar-info .progress-bar {
  background-color : #00CFE8;
}

.timeline .timeline-point-info {
  border-color : #00CFE8 !important;
}

.timeline .timeline-point-info i, .timeline .timeline-point-info svg {
  stroke : #00CFE8 !important;
}

.timeline .timeline-point-info.timeline-point-indicator {
  background-color : #00CFE8 !important;
}

.timeline .timeline-point-info.timeline-point-indicator:before {
  background : rgba(0, 207, 232, 0.12) !important;
}

.divider.divider-info .divider-text:before, .divider.divider-info .divider-text:after {
  border-color : #00CFE8 !important;
}

input:focus ~ .bg-info {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #00CFE8 !important;
}

.form-check-info .form-check-input:checked {
  border-color : #00CFE8;
  background-color : #00CFE8;
}

.form-check-info .form-check-input:not(:disabled):checked, .form-check-info .form-check-input:not(:disabled):focus {
  border-color : #00CFE8;
  box-shadow : 0 2px 4px 0 rgba(0, 207, 232, 0.4);
}

.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background : #00CFE8 !important;
  border-color : #00CFE8 !important;
}

.bg-warning .card-header, .bg-warning .card-footer {
  background-color : transparent;
}

.alert-warning {
  background : rgba(255, 159, 67, 0.12) !important;
  color : #FF9F43 !important;
}

.alert-warning .alert-heading {
  box-shadow : rgba(255, 159, 67, 0.4) 0 6px 15px -7px;
}

.alert-warning .alert-link {
  color : #FF922A !important;
}

.alert-warning .btn-close {
  background : transparent url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%23ff9f43\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat;
  color : #FF9F43 !important;
}

.bg-light-warning {
  background : rgba(255, 159, 67, 0.12) !important;
  color : #FF9F43 !important;
}

.bg-light-warning.fc-h-event, .bg-light-warning.fc-v-event {
  border-color : rgba(255, 159, 67, 0.1);
}

.bg-light-warning .fc-list-event-dot {
  border-color : #FF9F43 !important;
}

.bg-light-warning.fc-list-event:hover td {
  background : rgba(255, 159, 67, 0.1) !important;
}

.bg-light-warning.fc-list-event .fc-list-event-title {
  color : #6E6B7B;
}

.avatar.bg-light-warning {
  color : #FF9F43 !important;
}

.border-warning {
  border : 1px solid #FF9F43 !important;
}

.border-top-warning {
  border-top : 1px solid #FF9F43;
}

.border-bottom-warning {
  border-bottom : 1px solid #FF9F43;
}

.border-start-warning {
  border-right : 1px solid #FF9F43;
}

.border-end-warning {
  border-left : 1px solid #FF9F43;
}

.bg-warning.badge-glow, .border-warning.badge-glow {
  box-shadow : 0 0 10px #FF9F43;
}

.badge.badge-light-warning {
  background-color : rgba(255, 159, 67, 0.12);
  color : #FF9F43 !important;
}

.overlay-warning {
  background : #FF9F43;
  /* The Fallback */
  background : rgba(255, 159, 67, 0.6);
}

.btn-warning {
  border-color : #FF9F43 !important;
  background-color : #FF9F43 !important;
  color : #FFFFFF !important;
}

.btn-warning:focus, .btn-warning:active, .btn-warning.active {
  color : #FFFFFF;
  background-color : #FF922A !important;
}

.btn-warning:hover:not(.disabled):not(:disabled) {
  box-shadow : 0 8px 25px -8px #FF9F43;
}

.btn-warning:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-check:checked + .btn-warning, .btn-check:active + .btn-warning {
  color : #FFFFFF;
  background-color : #FF922A !important;
}

.btn-flat-warning {
  background-color : transparent;
  color : #FF9F43;
}

.btn-flat-warning:hover {
  color : #FF9F43;
}

.btn-flat-warning:hover:not(.disabled):not(:disabled) {
  background-color : rgba(255, 159, 67, 0.12);
}

.btn-flat-warning:active, .btn-flat-warning.active, .btn-flat-warning:focus {
  background-color : rgba(255, 159, 67, 0.2);
  color : #FF9F43;
}

.btn-flat-warning.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ff9f43\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-relief-warning {
  background-color : #FF9F43;
  box-shadow : inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
}

.btn-relief-warning:hover:not(.disabled):not(:disabled) {
  background-color : #FFAC5D;
}

.btn-relief-warning:active, .btn-relief-warning.active, .btn-relief-warning:focus {
  background-color : #FF922A;
}

.btn-relief-warning:hover {
  color : #FFFFFF;
}

.btn-relief-warning:active, .btn-relief-warning.active {
  outline : none;
  box-shadow : none;
  -webkit-transform : translateY(3px);
      -ms-transform : translateY(3px);
          transform : translateY(3px);
}

.btn-outline-warning {
  border : 1px solid #FF9F43 !important;
  background-color : transparent;
  color : #FF9F43;
}

.btn-outline-warning:hover:not(.disabled):not(:disabled) {
  background-color : rgba(255, 159, 67, 0.04);
  color : #FF9F43;
}

.btn-outline-warning:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-outline-warning:not(:disabled):not(.disabled):active, .btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):focus {
  background-color : rgba(255, 159, 67, 0.2);
  color : #FF9F43;
}

.btn-outline-warning.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ff9f43\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-outline-warning.show.dropdown-toggle {
  background-color : rgba(255, 159, 67, 0.2);
  color : #FF9F43;
}

.btn-check:checked + .btn-outline-warning, .btn-check:active + .btn-outline-warning {
  color : #FF9F43;
  background-color : rgba(255, 159, 67, 0.2) !important;
}

.btn-outline-warning.waves-effect .waves-ripple, .btn-flat-warning.waves-effect .waves-ripple {
  background : -webkit-radial-gradient(rgba(255, 159, 67, 0.2) 0, rgba(255, 159, 67, 0.3) 40%, rgba(255, 159, 67, 0.4) 50%, rgba(255, 159, 67, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background :         radial-gradient(rgba(255, 159, 67, 0.2) 0, rgba(255, 159, 67, 0.3) 40%, rgba(255, 159, 67, 0.4) 50%, rgba(255, 159, 67, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.bullet.bullet-warning {
  background-color : #FF9F43;
}

.modal.modal-warning .modal-header .modal-title {
  color : #FF9F43;
}

.modal.modal-warning .modal-header .btn-close {
  background : #FFFFFF url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%23ff9f43\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat !important;
  color : #FF9F43 !important;
}

.pagination-warning .page-item.active .page-link {
  background : #FF9F43 !important;
  color : #FFFFFF;
}

.pagination-warning .page-item.active .page-link:hover {
  color : #FFFFFF;
}

.pagination-warning .page-item .page-link:hover {
  color : #FF9F43;
}

.pagination-warning .page-item.prev-item .page-link:hover, .pagination-warning .page-item.next-item .page-link:hover {
  background : #FF9F43;
  color : #FFFFFF;
}

.pagination-warning .page-item.next-item .page-link:active:after, .pagination-warning .page-item.next-item .page-link:hover:after, .pagination-warning .page-item.next .page-link:active:after, .pagination-warning .page-item.next .page-link:hover:after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ff9f43\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.pagination-warning .page-item.prev-item .page-link:active:before, .pagination-warning .page-item.prev-item .page-link:hover:before, .pagination-warning .page-item.prev .page-link:active:before, .pagination-warning .page-item.prev .page-link:hover:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ff9f43\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-left\'%3E%3Cpolyline points=\'15 18 9 12 15 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.nav-pill-warning .nav-item .nav-link.active {
  color : #FFFFFF;
  background-color : #FF9F43 !important;
  border-color : #FF9F43;
  box-shadow : 0 4px 18px -4px rgba(255, 159, 67, 0.65);
}

.progress-bar-warning {
  background-color : rgba(255, 159, 67, 0.12);
}

.progress-bar-warning .progress-bar {
  background-color : #FF9F43;
}

.timeline .timeline-point-warning {
  border-color : #FF9F43 !important;
}

.timeline .timeline-point-warning i, .timeline .timeline-point-warning svg {
  stroke : #FF9F43 !important;
}

.timeline .timeline-point-warning.timeline-point-indicator {
  background-color : #FF9F43 !important;
}

.timeline .timeline-point-warning.timeline-point-indicator:before {
  background : rgba(255, 159, 67, 0.12) !important;
}

.divider.divider-warning .divider-text:before, .divider.divider-warning .divider-text:after {
  border-color : #FF9F43 !important;
}

input:focus ~ .bg-warning {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #FF9F43 !important;
}

.form-check-warning .form-check-input:checked {
  border-color : #FF9F43;
  background-color : #FF9F43;
}

.form-check-warning .form-check-input:not(:disabled):checked, .form-check-warning .form-check-input:not(:disabled):focus {
  border-color : #FF9F43;
  box-shadow : 0 2px 4px 0 rgba(255, 159, 67, 0.4);
}

.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background : #FF9F43 !important;
  border-color : #FF9F43 !important;
}

.bg-danger .card-header, .bg-danger .card-footer {
  background-color : transparent;
}

.alert-danger {
  background : rgba(234, 84, 85, 0.12) !important;
  color : #EA5455 !important;
}

.alert-danger .alert-heading {
  box-shadow : rgba(234, 84, 85, 0.4) 0 6px 15px -7px;
}

.alert-danger .alert-link {
  color : #E73D3E !important;
}

.alert-danger .btn-close {
  background : transparent url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%23ea5455\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat;
  color : #EA5455 !important;
}

.bg-light-danger {
  background : rgba(234, 84, 85, 0.12) !important;
  color : #EA5455 !important;
}

.bg-light-danger.fc-h-event, .bg-light-danger.fc-v-event {
  border-color : rgba(234, 84, 85, 0.1);
}

.bg-light-danger .fc-list-event-dot {
  border-color : #EA5455 !important;
}

.bg-light-danger.fc-list-event:hover td {
  background : rgba(234, 84, 85, 0.1) !important;
}

.bg-light-danger.fc-list-event .fc-list-event-title {
  color : #6E6B7B;
}

.avatar.bg-light-danger {
  color : #EA5455 !important;
}

.border-danger {
  border : 1px solid #EA5455 !important;
}

.border-top-danger {
  border-top : 1px solid #EA5455;
}

.border-bottom-danger {
  border-bottom : 1px solid #EA5455;
}

.border-start-danger {
  border-right : 1px solid #EA5455;
}

.border-end-danger {
  border-left : 1px solid #EA5455;
}

.bg-danger.badge-glow, .border-danger.badge-glow {
  box-shadow : 0 0 10px #EA5455;
}

.badge.badge-light-danger {
  background-color : rgba(234, 84, 85, 0.12);
  color : #EA5455 !important;
}

.overlay-danger {
  background : #EA5455;
  /* The Fallback */
  background : rgba(234, 84, 85, 0.6);
}

.btn-danger {
  border-color : #EA5455 !important;
  background-color : #EA5455 !important;
  color : #FFFFFF !important;
}

.btn-danger:focus, .btn-danger:active, .btn-danger.active {
  color : #FFFFFF;
  background-color : #E73D3E !important;
}

.btn-danger:hover:not(.disabled):not(:disabled) {
  box-shadow : 0 8px 25px -8px #EA5455;
}

.btn-danger:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-check:checked + .btn-danger, .btn-check:active + .btn-danger {
  color : #FFFFFF;
  background-color : #E73D3E !important;
}

.btn-flat-danger {
  background-color : transparent;
  color : #EA5455;
}

.btn-flat-danger:hover {
  color : #EA5455;
}

.btn-flat-danger:hover:not(.disabled):not(:disabled) {
  background-color : rgba(234, 84, 85, 0.12);
}

.btn-flat-danger:active, .btn-flat-danger.active, .btn-flat-danger:focus {
  background-color : rgba(234, 84, 85, 0.2);
  color : #EA5455;
}

.btn-flat-danger.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ea5455\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-relief-danger {
  background-color : #EA5455;
  box-shadow : inset 0 -3px 0 0 rgba(34, 41, 47, 0.2);
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
}

.btn-relief-danger:hover:not(.disabled):not(:disabled) {
  background-color : #ED6B6C;
}

.btn-relief-danger:active, .btn-relief-danger.active, .btn-relief-danger:focus {
  background-color : #E73D3E;
}

.btn-relief-danger:hover {
  color : #FFFFFF;
}

.btn-relief-danger:active, .btn-relief-danger.active {
  outline : none;
  box-shadow : none;
  -webkit-transform : translateY(3px);
      -ms-transform : translateY(3px);
          transform : translateY(3px);
}

.btn-outline-danger {
  border : 1px solid #EA5455 !important;
  background-color : transparent;
  color : #EA5455;
}

.btn-outline-danger:hover:not(.disabled):not(:disabled) {
  background-color : rgba(234, 84, 85, 0.04);
  color : #EA5455;
}

.btn-outline-danger:not(:disabled):not(.disabled):active:focus {
  box-shadow : none;
}

.btn-outline-danger:not(:disabled):not(.disabled):active, .btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):focus {
  background-color : rgba(234, 84, 85, 0.2);
  color : #EA5455;
}

.btn-outline-danger.dropdown-toggle::after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ea5455\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-down\'%3E%3Cpolyline points=\'6 9 12 15 18 9\'%3E%3C/polyline%3E%3C/svg%3E');
}

.btn-outline-danger.show.dropdown-toggle {
  background-color : rgba(234, 84, 85, 0.2);
  color : #EA5455;
}

.btn-check:checked + .btn-outline-danger, .btn-check:active + .btn-outline-danger {
  color : #EA5455;
  background-color : rgba(234, 84, 85, 0.2) !important;
}

.btn-outline-danger.waves-effect .waves-ripple, .btn-flat-danger.waves-effect .waves-ripple {
  background : -webkit-radial-gradient(rgba(234, 84, 85, 0.2) 0, rgba(234, 84, 85, 0.3) 40%, rgba(234, 84, 85, 0.4) 50%, rgba(234, 84, 85, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  background :         radial-gradient(rgba(234, 84, 85, 0.2) 0, rgba(234, 84, 85, 0.3) 40%, rgba(234, 84, 85, 0.4) 50%, rgba(234, 84, 85, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.bullet.bullet-danger {
  background-color : #EA5455;
}

.modal.modal-danger .modal-header .modal-title {
  color : #EA5455;
}

.modal.modal-danger .modal-header .btn-close {
  background : #FFFFFF url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' fill=\'%23ea5455\'%3e%3cpath d=\'M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z\'/%3e%3c/svg%3e') center/0.75rem auto no-repeat !important;
  color : #EA5455 !important;
}

.pagination-danger .page-item.active .page-link {
  background : #EA5455 !important;
  color : #FFFFFF;
}

.pagination-danger .page-item.active .page-link:hover {
  color : #FFFFFF;
}

.pagination-danger .page-item .page-link:hover {
  color : #EA5455;
}

.pagination-danger .page-item.prev-item .page-link:hover, .pagination-danger .page-item.next-item .page-link:hover {
  background : #EA5455;
  color : #FFFFFF;
}

.pagination-danger .page-item.next-item .page-link:active:after, .pagination-danger .page-item.next-item .page-link:hover:after, .pagination-danger .page-item.next .page-link:active:after, .pagination-danger .page-item.next .page-link:hover:after {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ea5455\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.pagination-danger .page-item.prev-item .page-link:active:before, .pagination-danger .page-item.prev-item .page-link:hover:before, .pagination-danger .page-item.prev .page-link:active:before, .pagination-danger .page-item.prev .page-link:hover:before {
  background-image : url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23ea5455\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-left\'%3E%3Cpolyline points=\'15 18 9 12 15 6\'%3E%3C/polyline%3E%3C/svg%3E') !important;
}

.nav-pill-danger .nav-item .nav-link.active {
  color : #FFFFFF;
  background-color : #EA5455 !important;
  border-color : #EA5455;
  box-shadow : 0 4px 18px -4px rgba(234, 84, 85, 0.65);
}

.progress-bar-danger {
  background-color : rgba(234, 84, 85, 0.12);
}

.progress-bar-danger .progress-bar {
  background-color : #EA5455;
}

.timeline .timeline-point-danger {
  border-color : #EA5455 !important;
}

.timeline .timeline-point-danger i, .timeline .timeline-point-danger svg {
  stroke : #EA5455 !important;
}

.timeline .timeline-point-danger.timeline-point-indicator {
  background-color : #EA5455 !important;
}

.timeline .timeline-point-danger.timeline-point-indicator:before {
  background : rgba(234, 84, 85, 0.12) !important;
}

.divider.divider-danger .divider-text:before, .divider.divider-danger .divider-text:after {
  border-color : #EA5455 !important;
}

input:focus ~ .bg-danger {
  box-shadow : 0 0 0 0.075rem #FFFFFF, 0 0 0 0.21rem #EA5455 !important;
}

.form-check-danger .form-check-input:checked {
  border-color : #EA5455;
  background-color : #EA5455;
}

.form-check-danger .form-check-input:not(:disabled):checked, .form-check-danger .form-check-input:not(:disabled):focus {
  border-color : #EA5455;
  box-shadow : 0 2px 4px 0 rgba(234, 84, 85, 0.4);
}

.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background : #EA5455 !important;
  border-color : #EA5455 !important;
}

.bg-gradient-dark, .btn-gradient-dark {
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  background-image : -webkit-linear-gradient(137deg, #4B4B4B, #1E1E1E);
  background-image :         linear-gradient(-47deg, #4B4B4B, #1E1E1E);
  background-repeat : repeat;
}

.dark-layout .bg-gradient-dark, .dark-layout
.btn-gradient-dark {
  background-image : -webkit-linear-gradient(137deg, #1E1E1E, #4B4B4B);
  background-image :         linear-gradient(-47deg, #1E1E1E, #4B4B4B);
  background-repeat : repeat;
}

.bg-gradient-dark:hover, .bg-gradient-dark:active, .btn-gradient-dark:hover, .btn-gradient-dark:active {
  color : #FFFFFF;
}

.bg-gradient-dark:hover:not(.disabled):not(:disabled), .btn-gradient-dark:hover:not(.disabled):not(:disabled) {
  -webkit-transform : translateY(-2px);
      -ms-transform : translateY(-2px);
          transform : translateY(-2px);
}

.bg-gradient-dark:active, .btn-gradient-dark:active {
  -webkit-transform : translateY(0);
      -ms-transform : translateY(0);
          transform : translateY(0);
}

.bg-gradient-dark:active, .bg-gradient-dark:focus, .btn-gradient-dark:active, .btn-gradient-dark:focus {
  background-image : -webkit-linear-gradient(137deg, #1E1E1E, #4B4B4B);
  background-image :         linear-gradient(-47deg, #1E1E1E, #4B4B4B);
  background-repeat : repeat;
}

.bg-gradient-primary, .btn-gradient-primary {
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  background-image : -webkit-linear-gradient(137deg, #7367F0, #9E95F5);
  background-image :         linear-gradient(-47deg, #7367F0, #9E95F5);
  background-repeat : repeat;
}

.bg-gradient-primary:hover, .bg-gradient-primary:active, .btn-gradient-primary:hover, .btn-gradient-primary:active {
  color : #FFFFFF;
}

.bg-gradient-primary:hover:not(.disabled):not(:disabled), .btn-gradient-primary:hover:not(.disabled):not(:disabled) {
  -webkit-transform : translateY(-2px);
      -ms-transform : translateY(-2px);
          transform : translateY(-2px);
}

.bg-gradient-primary:active, .btn-gradient-primary:active {
  -webkit-transform : translateY(0);
      -ms-transform : translateY(0);
          transform : translateY(0);
}

.bg-gradient-primary:active, .bg-gradient-primary:focus, .btn-gradient-primary:active, .btn-gradient-primary:focus {
  background-image : -webkit-linear-gradient(137deg, #4839EB, #7367F0);
  background-image :         linear-gradient(-47deg, #4839EB, #7367F0);
  background-repeat : repeat;
}

.bg-gradient-secondary, .btn-gradient-secondary {
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  background-image : -webkit-linear-gradient(137deg, #82868B, #9CA0A4);
  background-image :         linear-gradient(-47deg, #82868B, #9CA0A4);
  background-repeat : repeat;
}

.bg-gradient-secondary:hover, .bg-gradient-secondary:active, .btn-gradient-secondary:hover, .btn-gradient-secondary:active {
  color : #FFFFFF;
}

.bg-gradient-secondary:hover:not(.disabled):not(:disabled), .btn-gradient-secondary:hover:not(.disabled):not(:disabled) {
  -webkit-transform : translateY(-2px);
      -ms-transform : translateY(-2px);
          transform : translateY(-2px);
}

.bg-gradient-secondary:active, .btn-gradient-secondary:active {
  -webkit-transform : translateY(0);
      -ms-transform : translateY(0);
          transform : translateY(0);
}

.bg-gradient-secondary:active, .bg-gradient-secondary:focus, .btn-gradient-secondary:active, .btn-gradient-secondary:focus {
  background-image : -webkit-linear-gradient(137deg, #696D71, #82868B);
  background-image :         linear-gradient(-47deg, #696D71, #82868B);
  background-repeat : repeat;
}

.bg-gradient-success, .btn-gradient-success {
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  background-image : -webkit-linear-gradient(137deg, #28C76F, #48DA89);
  background-image :         linear-gradient(-47deg, #28C76F, #48DA89);
  background-repeat : repeat;
}

.bg-gradient-success:hover, .bg-gradient-success:active, .btn-gradient-success:hover, .btn-gradient-success:active {
  color : #FFFFFF;
}

.bg-gradient-success:hover:not(.disabled):not(:disabled), .btn-gradient-success:hover:not(.disabled):not(:disabled) {
  -webkit-transform : translateY(-2px);
      -ms-transform : translateY(-2px);
          transform : translateY(-2px);
}

.bg-gradient-success:active, .btn-gradient-success:active {
  -webkit-transform : translateY(0);
      -ms-transform : translateY(0);
          transform : translateY(0);
}

.bg-gradient-success:active, .bg-gradient-success:focus, .btn-gradient-success:active, .btn-gradient-success:focus {
  background-image : -webkit-linear-gradient(137deg, #1F9D57, #28C76F);
  background-image :         linear-gradient(-47deg, #1F9D57, #28C76F);
  background-repeat : repeat;
}

.bg-gradient-info, .btn-gradient-info {
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  background-image : -webkit-linear-gradient(137deg, #00CFE8, #1CE7FF);
  background-image :         linear-gradient(-47deg, #00CFE8, #1CE7FF);
  background-repeat : repeat;
}

.bg-gradient-info:hover, .bg-gradient-info:active, .btn-gradient-info:hover, .btn-gradient-info:active {
  color : #FFFFFF;
}

.bg-gradient-info:hover:not(.disabled):not(:disabled), .btn-gradient-info:hover:not(.disabled):not(:disabled) {
  -webkit-transform : translateY(-2px);
      -ms-transform : translateY(-2px);
          transform : translateY(-2px);
}

.bg-gradient-info:active, .btn-gradient-info:active {
  -webkit-transform : translateY(0);
      -ms-transform : translateY(0);
          transform : translateY(0);
}

.bg-gradient-info:active, .bg-gradient-info:focus, .btn-gradient-info:active, .btn-gradient-info:focus {
  background-image : -webkit-linear-gradient(137deg, #00A1B5, #00CFE8);
  background-image :         linear-gradient(-47deg, #00A1B5, #00CFE8);
  background-repeat : repeat;
}

.bg-gradient-warning, .btn-gradient-warning {
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  background-image : -webkit-linear-gradient(137deg, #FF9F43, #FFB976);
  background-image :         linear-gradient(-47deg, #FF9F43, #FFB976);
  background-repeat : repeat;
}

.bg-gradient-warning:hover, .bg-gradient-warning:active, .btn-gradient-warning:hover, .btn-gradient-warning:active {
  color : #FFFFFF;
}

.bg-gradient-warning:hover:not(.disabled):not(:disabled), .btn-gradient-warning:hover:not(.disabled):not(:disabled) {
  -webkit-transform : translateY(-2px);
      -ms-transform : translateY(-2px);
          transform : translateY(-2px);
}

.bg-gradient-warning:active, .btn-gradient-warning:active {
  -webkit-transform : translateY(0);
      -ms-transform : translateY(0);
          transform : translateY(0);
}

.bg-gradient-warning:active, .bg-gradient-warning:focus, .btn-gradient-warning:active, .btn-gradient-warning:focus {
  background-image : -webkit-linear-gradient(137deg, #FF8510, #FF9F43);
  background-image :         linear-gradient(-47deg, #FF8510, #FF9F43);
  background-repeat : repeat;
}

.bg-gradient-danger, .btn-gradient-danger {
  color : #FFFFFF;
  -webkit-transition : all 0.2s ease;
          transition : all 0.2s ease;
  background-image : -webkit-linear-gradient(137deg, #EA5455, #F08182);
  background-image :         linear-gradient(-47deg, #EA5455, #F08182);
  background-repeat : repeat;
}

.bg-gradient-danger:hover, .bg-gradient-danger:active, .btn-gradient-danger:hover, .btn-gradient-danger:active {
  color : #FFFFFF;
}

.bg-gradient-danger:hover:not(.disabled):not(:disabled), .btn-gradient-danger:hover:not(.disabled):not(:disabled) {
  -webkit-transform : translateY(-2px);
      -ms-transform : translateY(-2px);
          transform : translateY(-2px);
}

.bg-gradient-danger:active, .btn-gradient-danger:active {
  -webkit-transform : translateY(0);
      -ms-transform : translateY(0);
          transform : translateY(0);
}

.bg-gradient-danger:active, .bg-gradient-danger:focus, .btn-gradient-danger:active, .btn-gradient-danger:focus {
  background-image : -webkit-linear-gradient(137deg, #E42728, #EA5455);
  background-image :         linear-gradient(-47deg, #E42728, #EA5455);
  background-repeat : repeat;
}