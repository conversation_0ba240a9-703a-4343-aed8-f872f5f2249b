/********* CONTEXT MENU *********/
.context-menu-list {
  margin : 0;
  padding : 0.5rem 0;
  border-radius : 0.357rem;
  border : 1px solid rgba(34, 41, 47, 0.05);
  box-shadow : 0 5px 25px rgba(34, 41, 47, 0.1);
  min-width : 10rem;
}

.context-menu-list .context-menu-item {
  padding : 0.65rem 1.28rem;
  color : #6E6B7B;
}

.context-menu-list .context-menu-item.context-menu-submenu:after {
  border-color : transparent #6E6B7B transparent transparent;
}

.context-menu-list .context-menu-item.context-menu-hover, .context-menu-list .context-menu-item:hover, .context-menu-list .context-menu-item:focus {
  background-color : rgba(115, 103, 240, 0.12) !important;
  color : #7367F0;
}

.context-menu-list .context-menu-item.context-menu-hover.context-menu-submenu:after, .context-menu-list .context-menu-item:hover.context-menu-submenu:after, .context-menu-list .context-menu-item:focus.context-menu-submenu:after {
  border-color : transparent #7367F0 transparent transparent !important;
}

.context-menu-list .context-menu-item:focus {
  outline : 0;
}

.dark-layout .context-menu-list {
  background-color : #161D31;
  border-color : #3B4253;
}

.dark-layout .context-menu-list .context-menu-item {
  background-color : #161D31;
}

.dark-layout .context-menu-list .context-menu-item span {
  color : #B4B7BD;
}

.dark-layout .context-menu-list .context-menu-item.context-menu-hover > span {
  color : #7367F0;
}

.dark-layout .context-menu-list .context-menu-item.context-menu-submenu:after {
  border-color : transparent #B4B7BD transparent transparent;
}

[data-textdirection='rtl'] .context-menu-list {
  z-index : 1031 !important;
}

[data-textdirection='rtl'] .context-menu-list .context-menu-item.context-menu-submenu:after {
  -webkit-transform : rotate(-180deg);
      -ms-transform : rotate(-180deg);
          transform : rotate(-180deg);
  top : 1.2rem;
  left : 1rem;
  right : auto;
  border-color : transparent transparent transparent #6E6B7B;
}

[data-textdirection='rtl'] .context-menu-list .context-menu-item.context-menu-hover.context-menu-submenu:after {
  border-color : transparent transparent transparent #7367F0 !important;
}

[data-textdirection='rtl'] .context-menu-list .context-menu-item > .context-menu-list {
  right : 100%;
  margin-right : 0;
}

[data-textdirection='rtl'] .dark-layout .context-menu-list .context-menu-item.context-menu-submenu:after {
  border-color : transparent transparent transparent #B4B7BD;
}