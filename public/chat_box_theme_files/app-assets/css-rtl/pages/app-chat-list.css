.chat-app-window .user-chats {
  background-color : #F2F0F7;
  padding : 1rem;
  position : relative;
  height : calc(100% - 65px - 65px);
}

.chat-app-window .user-chats .avatar img {
  border : 2px solid #FFFFFF;
}

.chat-app-window .active-chat {
  height : inherit;
}

.chat-app-window .active-chat .chat-header {
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-pack : justify;
  -webkit-justify-content : space-between;
  -ms-flex-pack : justify;
          justify-content : space-between;
  height : 65px;
  background-color : #FFFFFF;
  padding : 0 1rem;
  border-bottom : 1px solid #EBE9F1;
}

.chat-app-window .chats .chat-avatar {
  float : left;
}

.chat-app-window .chats .chat-body {
  display : block;
  margin : 10px 0 0 30px;
  overflow : hidden;
}

.chat-app-window .chats .chat-body .chat-content {
  float : left;
  padding : 0.7rem 1rem;
  margin : 0 0 10px 1rem;
  clear : both;
  color : #FFFFFF;
  background-image : -webkit-linear-gradient(170deg, #7367F0, #9E95F5);
  background-image :         linear-gradient(-80deg, #7367F0, #9E95F5);
  border-radius : 0.357rem;
  box-shadow : 0 4px 8px 0 rgba(34, 41, 47, 0.12);
  max-width : 75%;
}

.chat-app-window .chats .chat-body .chat-content p {
  margin : 0;
}

.chat-app-window .chats .chat-left .chat-avatar {
  float : right;
}

.chat-app-window .chats .chat-left .chat-body .chat-content {
  float : right;
  margin : 0 1rem 10px 0;
  color : #6E6B7B;
  background : none;
  background-color : white;
}

.chat-app-window .chat-app-form {
  height : 65px;
  padding : 0 1rem;
  background-color : #FFFFFF;
  display : -webkit-box;
  display : -webkit-flex;
  display : -ms-flexbox;
  display :         flex;
  -webkit-box-align : center;
  -webkit-align-items : center;
  -ms-flex-align : center;
          align-items : center;
  border-top : 1px solid #EBE9F1;
}

.chat-widget .card-header {
  padding-top : 0.8rem;
  padding-bottom : 0.8rem;
}

.chat-widget .chat-app-window .user-chats {
  background-color : #F8F8F8;
  height : 390px;
}

.chat-widget .chat-app-window .chat-app-form {
  border-top : 0;
  border-bottom-right-radius : 0.357rem;
  border-bottom-left-radius : 0.357rem;
  height : 56px;
}

.chat-widget .chat-app-window .chat-app-form .input-group-text, .chat-widget .chat-app-window .chat-app-form .message {
  border : 0;
  padding-right : 0;
}

.chat-widget .chat-app-window .chat-app-form .input-group:not(.bootstrap-touchspin):focus-within {
  box-shadow : none;
}