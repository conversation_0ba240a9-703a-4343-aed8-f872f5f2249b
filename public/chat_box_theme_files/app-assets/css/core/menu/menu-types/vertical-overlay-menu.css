/*=========================================================================================
    File Name: vertical-overlay-menu.scss
    Description: A overlay style vertical menu with show and hide support. It support
    light & dark version, filpped layout, right side icons, native scroll and borders menu
    item seperation.
    ----------------------------------------------------------------------------------------
    Item Name: Vuexy  - Vuejs, HTML & Laravel Admin Dashboard Template
    Author: PIXINVENT
    Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/
.vertical-overlay-menu .content {
  margin-left: 0; }

.vertical-overlay-menu .navbar .navbar-header {
  float: left;
  width: 260px; }

.vertical-overlay-menu .main-menu, .vertical-overlay-menu.menu-hide .main-menu {
  opacity: 0;
  transform: translate3d(0, 0, 0);
  transition: width 0.25s, opacity 0.25s, transform 0.25s;
  width: 260px;
  left: -260px; }

.vertical-overlay-menu .main-menu .navigation > li > a > svg,
.vertical-overlay-menu .main-menu .navigation > li > a > i {
  margin-right: 14px;
  float: left;
  transition: 200ms ease all;
  height: 20px;
  width: 20px; }

.vertical-overlay-menu .main-menu .navigation > li > a > svg:before,
.vertical-overlay-menu .main-menu .navigation > li > a > i:before {
  transition: 200ms ease all;
  font-size: 1.429rem; }

.vertical-overlay-menu .main-menu .navigation li.has-sub > a:after {
  content: '';
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236e6b7b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-chevron-right'%3E%3Cpolyline points='9 18 15 12 9 6'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 1rem;
  height: 1rem;
  width: 1rem;
  display: inline-block;
  position: absolute;
  right: 20px;
  top: 14px;
  transform: rotate(0deg);
  transition: all 0.2s ease-out; }

.vertical-overlay-menu .main-menu .navigation li.has-sub.open:not(.menu-item-closing) > a:after {
  transform: rotate(90deg); }

.vertical-overlay-menu .main-menu .navigation .navigation-header .feather-more-horizontal {
  display: none; }

.vertical-overlay-menu.menu-open .main-menu {
  opacity: 1;
  transform: translate3d(260px, 0, 0);
  transition: width 0.25s, opacity 0.25s, transform 0.25s; }
