.draggable {
  cursor: grab; }

.gu-unselectable .draggable {
  cursor: grabbing; }

#multiple-list-group-a,
#multiple-list-group-b {
  min-height: 5.714rem; }

#dd-with-handle .list-group {
  min-height: 5.714rem; }

#dd-with-handle .list-group .handle {
  padding: 0 5px;
  margin-right: 5px;
  background-color: rgba(34, 41, 47, 0.1);
  cursor: move;
  font-size: 1.2rem; }

.gu-mirror .card {
  margin: 0 1rem; }

.gu-mirror .handle {
  padding: 0 5px;
  margin-right: 5px;
  background-color: rgba(34, 41, 47, 0.1);
  cursor: move;
  font-size: 1.2rem; }

.dark-layout .gu-mirror {
  color: #b4b7bd; }

.dark-layout .gu-mirror:not(.badge):not([class*='col-']) {
  background-color: #283046;
  border-color: #3b4253;
  box-shadow: 0 4px 24px 0 rgba(34, 41, 47, 0.24); }
