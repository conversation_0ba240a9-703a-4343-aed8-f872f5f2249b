<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg id="svg548" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="512" width="512" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3052">
  <rdf:RDF>
   <cc:Work rdf:about="">
  <dc:format>image/svg+xml</dc:format>
  <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs550">
  <clipPath id="clipPath5174" clipPathUnits="userSpaceOnUse">
   <rect id="rect5176" fill-opacity="0.67" height="496.06" width="496.06" y="-.000032559" x="0"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath5174)" transform="matrix(1.0321 0 0 1.0321 0 .000033605)" stroke-width="1pt">
  <rect id="rect551" height="165.37" width="992.13" y="330.7" x="0"/>
  <rect id="rect552" height="165.37" width="992.13" y="165.33" x="0" fill="#fff"/>
  <rect id="rect553" height="165.37" width="992.86" y="-.000015" x="0" fill="#f00"/>
  <path id="path556" d="m0 0.000076294v496.06l330.7-248.03-330.7-248.03z" fill="#009a00"/>
 </g>
</svg>
