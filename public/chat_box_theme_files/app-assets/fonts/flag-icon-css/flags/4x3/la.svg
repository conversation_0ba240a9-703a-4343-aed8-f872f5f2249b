<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg id="svg378" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns="http://www.w3.org/2000/svg" height="480" width="640" version="1.1" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/">
 <metadata id="metadata3092">
  <rdf:RDF>
   <cc:Work rdf:about="">
  <dc:format>image/svg+xml</dc:format>
  <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs id="defs380">
  <clipPath id="clipPath5023" clipPathUnits="userSpaceOnUse">
   <rect id="rect5025" fill-opacity="0.67" height="480" width="640" y="0" x="0"/>
  </clipPath>
 </defs>
 <g id="flag" fill-rule="evenodd" clip-path="url(#clipPath5023)">
  <rect id="rect171" height="480" width="720" y="0" x="-40" stroke-width="1pt" fill="#e90012"/>
  <rect id="rect403" height="241.48" width="720" y="119.26" x="-40" fill="#003dd2"/>
  <path id="path138" d="m678.37 357.48a141.78 141.78 0 1 1 -283.56 0 141.78 141.78 0 1 1 283.56 0z" transform="matrix(.72943 0 0 .72943 -71.404 -20.759)" fill="#fff"/>
 </g>
</svg>
