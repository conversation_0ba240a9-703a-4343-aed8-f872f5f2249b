$((function(){var e="../../../app-assets/",a=$(".credit-card-mask"),t=$("#editCardValidation"),n=$(".expiry-date-mask"),i=$(".cvv-code-mask");"laravel"===$("body").attr("data-framework")&&(e=$("body").attr("data-asset-path")),a.length&&a.each((function(){new Cleave($(this),{creditCard:!0,onCreditCardTypeChanged:function(a){document.querySelector(".edit-card-type").innerHTML=""!=a&&"unknown"!=a?'<img src="'+e+"images/icons/payments/"+a+'-cc.png" height="24"/>':""}})})),n.length&&n.each((function(){new Cleave($(this),{date:!0,delimiter:"/",datePattern:["m","y"]})})),i.length&&i.each((function(){new Cleave($(this),{numeral:!0,numeralPositiveOnly:!0})})),t.length&&t.validate({rules:{modalEditCard:{required:!0}}})}));