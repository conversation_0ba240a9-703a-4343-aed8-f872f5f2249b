!function(t,n,o){"use strict";o(".touchspin").TouchSpin({buttondown_class:"btn btn-primary",buttonup_class:"btn btn-primary",buttondown_txt:feather.icons.minus.toSvg(),buttonup_txt:feather.icons.plus.toSvg()}),o(".touchspin-icon").TouchSpin({buttondown_txt:feather.icons["chevron-down"].toSvg(),buttonup_txt:feather.icons["chevron-up"].toSvg()});var s=o(".touchspin-min-max");s.length>0&&s.TouchSpin({min:17,max:21,buttondown_txt:feather.icons.minus.toSvg(),buttonup_txt:feather.icons.plus.toSvg()}).on("touchspin.on.startdownspin",(function(){var t=o(this);o(".bootstrap-touchspin-up").removeClass("disabled-max-min"),17==t.val()&&o(this).siblings().find(".bootstrap-touchspin-down").addClass("disabled-max-min")})).on("touchspin.on.startupspin",(function(){var t=o(this);o(".bootstrap-touchspin-down").removeClass("disabled-max-min"),21==t.val()&&o(this).siblings().find(".bootstrap-touchspin-up").addClass("disabled-max-min")})),o(".touchspin-step").TouchSpin({step:5,buttondown_txt:feather.icons.minus.toSvg(),buttonup_txt:feather.icons.plus.toSvg()}),o(".touchspin-color").each((function(t){var n="btn btn-primary",s="btn btn-primary",i=o(this);i.data("bts-button-down-class")&&(n=i.data("bts-button-down-class")),i.data("bts-button-up-class")&&(s=i.data("bts-button-up-class")),i.TouchSpin({mousewheel:!1,buttondown_class:n,buttonup_class:s,buttondown_txt:feather.icons.minus.toSvg(),buttonup_txt:feather.icons.plus.toSvg()})}))}(window,document,jQuery);