
// (function ($) {
//     "use strict";

//     /*==================================================================
//     [ Validate after type ]*/
//     $('.inner_form .form-control').each(function () {
//         $(this).on('blur', function () {
//             if (validate(this) == false) {
//                 showValidate(this);
//             }
//             else {
//                 $(this).parent().addClass('true-validate');
//             }
//         })
//     })


//     /*==================================================================
//     [ Validate ]*/
//     var input = $('.inner_form .form-control');

//     $('.validate-form').on('submit', function () {
//         var check = true;

//         for (var i = 0; i < input.length; i++) {
//             if (validate(input[i]) == false) {
//                 showValidate(input[i]);
//                 check = false;
//             }
//         }

//         return check;
//     });


//     $('.validate-form .input100').each(function () {
//         $(this).focus(function () {
//             hideValidate(this);
//             $(this).parent().removeClass('true-validate');
//         });
//     });

//     function validate(input) {
//         if ($(input).attr('type') == 'email' || $(input).attr('name') == 'email') {
//             if ($(input).val().trim().match(/^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{1,5}|[0-9]{1,3})(\]?)$/) == null) {
//                 return false;
//             }
//         }
//         else {
//             if ($(input).val().trim() == '') {
//                 return false;
//             }
//         }
//     }

//     function showValidate(input) {
//         var thisAlert = $(input).parent();

//         $(thisAlert).addClass('alert-validate');

//         $(thisAlert).append('<span class="btn-hide-validate"><i class="fa fa-times"></i></span>')
//         $('.btn-hide-validate').each(function () {
//             $(this).on('click', function () {
//                 hideValidate(this);
//             });
//         });
//     }

//     function hideValidate(input) {
//         var thisAlert = $(input).parent();
//         $(thisAlert).removeClass('alert-validate');
//         $(thisAlert).find('.btn-hide-validate').remove();
//     }
// })(jQuery);

// /*-----------------*/
// /*-----------------*/



// $(".mobile_search").click(function () {
//     $(".searching").slideToggle("slow", function () {
//     });
// });

$(".close_full_menu").click(function () {
    $("body").toggleClass("close_navigation");
});

// /*----------*/
// $('.btn-number').click(function (e) {
//     e.preventDefault();

//     fieldName = $(this).attr('data-field');
//     type = $(this).attr('data-type');
//     var input = $("input[name='" + fieldName + "']");
//     var currentVal = parseInt(input.val());
//     if (!isNaN(currentVal)) {
//         if (type == 'minus') {

//             if (currentVal > input.attr('min')) {
//                 input.val(currentVal - 1).change();
//             }
//             if (parseInt(input.val()) == input.attr('min')) {
//                 $(this).attr('disabled', true);
//             }

//         } else if (type == 'plus') {

//             if (currentVal < input.attr('max')) {
//                 input.val(currentVal + 1).change();
//             }
//             if (parseInt(input.val()) == input.attr('max')) {
//                 $(this).attr('disabled', true);
//             }

//         }
//     } else {
//         input.val(0);
//     }
// });
// $('.input-number').focusin(function () {
//     $(this).data('oldValue', $(this).val());
// });
// $('.input-number').change(function () {

//     minValue = parseInt($(this).attr('min'));
//     maxValue = parseInt($(this).attr('max'));
//     valueCurrent = parseInt($(this).val());

//     name = $(this).attr('name');
//     if (valueCurrent >= minValue) {
//         $(".btn-number[data-type='minus'][data-field='" + name + "']").removeAttr('disabled')
//     } else {
//         alert('Sorry, the minimum value was reached');
//         $(this).val($(this).data('oldValue'));
//     }
//     if (valueCurrent <= maxValue) {
//         $(".btn-number[data-type='plus'][data-field='" + name + "']").removeAttr('disabled')
//     } else {
//         alert('Sorry, the maximum value was reached');
//         $(this).val($(this).data('oldValue'));
//     }


// });
// $(".input-number").keydown(function (e) {
//     // Allow: backspace, delete, tab, escape, enter and .
//     if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 190]) !== -1 ||
//         // Allow: Ctrl+A
//         (e.keyCode == 65 && e.ctrlKey === true) ||
//         // Allow: home, end, left, right
//         (e.keyCode >= 35 && e.keyCode <= 39)) {
//         // let it happen, don't do anything
//         return;
//     }
//     // Ensure that it is a number and stop the keypress
//     if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
//         e.preventDefault();
//     }
// });
// /*------*/
// $(document).ready(function () {
//     $('#dataTable').DataTable();
// });
// /*------*/
// if ($(window).width() <= 1024) {
//     $('body').addClass('close_navigation');
// } else {
//     $('body').removeClass('close_navigation');
// }
// /*------*/
// (function ($) {
//     'use strict';
//     $(function () {
//         $('.file-upload-browse').on('click', function () {
//             var file = $(this).parent().parent().parent().find('.file-upload-default');
//             file.trigger('click');
//         });
//         $('.file-upload-default').on('change', function () {
//             $(this).parent().find('.form-control').val($(this).val().replace(/C:\\fakepath\\/i, ''));
//         });
//     });
// })(jQuery);

