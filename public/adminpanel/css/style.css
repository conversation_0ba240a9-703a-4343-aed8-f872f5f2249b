html, body {
  height: 100%;
  min-height: 100%;
}

body {
  font-family: "Montserrat", sans-serif;
  background-color: #f4f5f8;
}

.logo_home {
  text-align: center;
  margin-bottom: 20px;
}

.login {
  height: 100vh;
  background-image: url("../images/login_bg.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
}

.inner_form {
  position: relative;
  z-index: 1;
  width: 490px;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 3px 20px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 3px 20px 0px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 3px 20px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 3px 20px 0px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 3px 20px 0px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  padding: 80px 55px 30px;
}
.inner_form h1 {
  text-align: center;
  font-size: 30px;
  color: #4b2354;
  text-transform: uppercase;
  margin-bottom: 20px;
  font-weight: 600;
}
.inner_form .inputs {
  position: relative;
}
.inner_form .inputs .btn-hide-validate {
  position: absolute;
  right: 15px;
  top: 20px;
  color: #b31f24;
}
.inner_form .form-control {
  border-radius: 20px;
  box-shadow: 0 5px 30px 0px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 5px 30px 0px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 5px 30px 0px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 5px 30px 0px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 5px 30px 0px rgba(0, 0, 0, 0.1);
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  transition: all 0.4s;
  height: 62px;
  font-size: 14px;
  margin-bottom: 15px;
  border: 0;
}
.inner_form .btn {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  min-width: 160px;
  height: 50px;
  background-color: #f84c72;
  border-radius: 25px;
  font-size: 14px;
  color: #fff;
  line-height: 1.2;
  text-transform: uppercase;
  -webkit-transition: all 0.4s;
  -o-transition: all 0.4s;
  -moz-transition: all 0.4s;
  transition: all 0.4s;
  box-shadow: 0 10px 30px 0px rgba(248, 76, 114, 0.5);
  -moz-box-shadow: 0 10px 30px 0px rgba(248, 76, 114, 0.5);
  -webkit-box-shadow: 0 10px 30px 0px rgba(248, 76, 114, 0.5);
  -o-box-shadow: 0 10px 30px 0px rgba(248, 76, 114, 0.5);
  -ms-box-shadow: 0 10px 30px 0px rgba(248, 76, 114, 0.5);
  margin: 25px auto;
}
.inner_form .btn:hover {
  background-color: #b31f24;
  box-shadow: 0 10px 30px 0px rgba(250, 132, 67, 0.5);
  -moz-box-shadow: 0 10px 30px 0px rgba(250, 132, 67, 0.5);
  -webkit-box-shadow: 0 10px 30px 0px rgba(250, 132, 67, 0.5);
  -o-box-shadow: 0 10px 30px 0px rgba(250, 132, 67, 0.5);
  -ms-box-shadow: 0 10px 30px 0px rgba(250, 132, 67, 0.5);
}

.alert-validate::before {
  content: attr(data-validate);
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  position: absolute;
  width: 100%;
  min-height: 62px;
  background-color: #fff;
  border-radius: 20px;
  top: 0px;
  left: 0px;
  padding: 0 45px 0 22px;
  pointer-events: none;
  font-size: 16px;
  color: #fa4251;
  line-height: 1.2;
}

/*------------
---------------DASHBOARD STYLING-------------------------------*/
.col-p-3 {
  padding-left: 3px !important;
  padding-right: 3px !important;
}

@media screen and (min-width: 767px) {
  .col-p-3 {
    flex: 0 0 20%;
    max-width: 20%;
  }
}
.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.navbar-menu-wrapper {
  background: #ffffff;
  transition: width 0.25s ease;
  -webkit-transition: width 0.25s ease;
  -moz-transition: width 0.25s ease;
  -ms-transition: width 0.25s ease;
  color: #9b9b9b;
  padding-left: 1.062rem;
  padding-right: 1.062rem;
  width: calc(100% - 257px);
  height: 60px;
  border-bottom: 1px solid #e3e3e3;
}
.navbar-menu-wrapper .navbar-nav .nav-item.nav-search {
  margin-left: 0rem;
}
.navbar-menu-wrapper .navbar-nav .nav-item.nav-search .input-group {
  background: #ececec;
  border-radius: 4px;
  padding: 0 0.75rem;
}
.navbar-menu-wrapper .navbar-nav .nav-item.nav-search .input-group .input-group-prepend {
  color: #c9c8c8;
  width: auto;
  border: none;
}
.navbar-menu-wrapper .navbar-nav .nav-item.nav-search .input-group i {
  color: #9b9b9b;
}
.navbar-menu-wrapper .navbar-nav .nav-item.nav-search .input-group .input-group-text {
  display: flex;
  align-items: center;
  padding: 0;
  margin-bottom: 0;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 0;
  border-radius: 0;
}
.navbar-menu-wrapper .navbar-nav .nav-item.nav-search .input-group .form-control {
  background-color: transparent;
  border: 0;
  box-shadow: none;
  font-size: 16px;
  height: 50px;
}
.navbar-menu-wrapper .navbar-nav .nav-item:last-child {
  margin-right: 0;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right {
  margin-left: auto;
  align-self: stretch !important;
  flex-direction: row !important;
  align-items: center;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item {
  color: #4F4F4F;
  font-size: 0.875rem;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item .nav-link {
  color: #a3a3a3;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item .nav-link.mobile_search {
  margin-right: 5px;
  display: none;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item .nav-link.mobile_search i {
  font-size: 1.5rem;
  margin-right: 0;
  vertical-align: middle;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .count-indicator {
  position: relative;
  padding: 0;
  text-align: center;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .count-indicator i {
  font-size: 1.5rem;
  margin-right: 0;
  vertical-align: middle;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .count-indicator .count {
  position: absolute;
  left: 59%;
  width: 8px;
  height: 8px;
  border-radius: 100%;
  background: #ff4747;
  top: 9px;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .count-indicator:after {
  display: none;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown {
  position: absolute;
  font-size: 0.9rem;
  margin-top: 0;
  right: 0;
  left: auto;
  top: 60px;
  border: none;
  -webkit-box-shadow: 0px 3px 21px 0px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0px 3px 21px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 3px 21px 0px rgba(0, 0, 0, 0.2);
  animation-name: dropdownAnimation;
  -webkit-animation-duration: 0.25s;
  -moz-animation-duration: 0.25s;
  -ms-animation-duration: 0.25s;
  -o-animation-duration: 0.25s;
  animation-duration: 0.25s;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -ms-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown .dropdown-item {
  display: flex;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #000;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  margin-bottom: 0;
  padding: 0.65rem 1.5rem;
  cursor: pointer;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown .dropdown-item:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown .dropdown-item .item-thumbnail img {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown .dropdown-item .item-thumbnail .item-icon {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center !important;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown .dropdown-item .item-thumbnail .item-icon i {
  font-size: 17px;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown .dropdown-item .item-content {
  padding-left: 0.937rem;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown .dropdown-item .ellipsis {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.nav-profile {
  margin-left: 1.8rem;
  margin-right: 1.8rem;
  white-space: nowrap;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.nav-profile .nav-link img {
  width: 32px;
  height: 32px;
  border-radius: 100%;
}
.navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.nav-profile .nav-link .nav-profile-name {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  color: #4a4a4a;
  font-weight: 500;
}

@keyframes dropdownAnimation {
  from {
    opacity: 0;
    transform: translate3d(0, -30px, 0);
  }
  to {
    opacity: 1;
    transform: none;
    transform: translate3d(0, 0px, 0);
  }
}
.content_area {
  min-height: calc(100vh);
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: row;
  flex-direction: row;
  padding-left: 0;
  padding-right: 0;
  position: relative;
}

.items_list li {
  margin-bottom: 8px;
}
.items_list li a.btn {
  font-size: 13px;
  width: 100%;
  white-space: break-spaces;
}

.close_navigation .sidebar {
  width: 50px;
}
.close_navigation .sidebar .top_brand_area .brand_logo {
  opacity: 0;
  visibility: hidden;
  width: 0;
  margin: 0;
}
.close_navigation .sidebar .nav .nav-item .nav-link span {
  margin-left: -10px;
  opacity: 0;
  visibility: hidden;
  transition: margin-left 0.3s linear, opacity 0.3s ease, visibility 0.3s ease;
  width: 0;
}
.close_navigation .sidebar .nav .nav-item .nav-link i.fa.fa-angle-down {
  margin-left: auto;
  padding-right: 0;
  opacity: 0;
  visibility: hidden;
  width: auto;
}
.close_navigation .active_user .user_cred {
  display: none;
}
.close_navigation .active_user .user_img {
  padding-right: 0;
}
.close_navigation .active_user .user_img img {
  width: 40px;
  height: 40px;
}
.close_navigation .pro_label p span, .close_navigation .pro_label p emp {
  opacity: 0;
  visibility: hidden;
}
.close_navigation h4 {
  opacity: 0;
  visibility: hidden;
}
.close_navigation .active_user {
  padding: 30px 10px 30px 4px;
}

.sidebar {
  background-color: #b31f24;
  font-weight: 400;
  padding: 0;
  width: 310px;
  min-height: 100%;
  transition: all 0.1s linear;
}
.sidebar h4 {
  font-size: 14px;
  color: #fff;
  text-transform: uppercase;
  margin-bottom: 0;
  padding: 0 10px;
}
.sidebar .navbar-toggler {
  box-shadow: none;
  outline: none;
  color: #b60006;
}
.sidebar .top_brand_area {
  display: flex;
  justify-content: space-between;
  box-shadow: -5px 1px 6px 1px rgba(0, 0, 0, 0.2);
  background-color: #fff;
}
.sidebar .top_brand_area .brand_logo {
  opacity: 1;
  visibility: visible;
  width: 170px;
}
.sidebar .nav {
  overflow: hidden;
  flex-direction: column;
  position: relative;
  padding-top: 15px;
  z-index: 1;
  background-color: #b60006;
}
.sidebar .nav.sub-menu {
  padding-top: 0;
  background-color: rgba(255, 255, 255, 0.1);
}
.sidebar .nav.sub-menu .nav-item .nav-link {
  padding: 10px 46px;
  font-size: 12px;
  font-weight: 500;
}
.sidebar .nav .nav-item .nav-link {
  color: #fff;
  font-size: 13px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
}
.sidebar .nav .nav-item .nav-link span {
  opacity: 1;
  visibility: visible;
  transition: margin-left 0.3s linear, opacity 0.3s ease, visibility 0.3s ease;
}
.sidebar .nav .nav-item .nav-link.active {
  background-color: rgba(255, 255, 255, 0.1);
}
.sidebar .nav .nav-item .nav-link i {
  vertical-align: bottom;
  width: 33px;
}
.sidebar .nav .nav-item .nav-link i img {
  width: 25px;
}
.sidebar .nav .nav-item .nav-link i.fa.fa-angle-down {
  margin-left: auto;
  padding-right: 0;
  opacity: 1;
  visibility: visible;
}
.sidebar .nav .nav-item .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.sidebar.menu_close {
  width: 0;
}
.sidebar.menu_close .nav {
  width: 0;
}

.active_user {
  display: flex;
  align-items: center;
  padding: 30px 10px 30px 10px;
}
.active_user .user_img {
  padding-right: 5px;
}
.active_user .user_img img {
  width: 46px;
  height: 46px;
  border: 3px solid #fff;
  border-radius: 50%;
  object-fit: cover;
  object-position: top;
}
.active_user .user_cred {
  padding-left: 5px;
}
.active_user .user_cred p {
  color: #fff;
  font-size: 14px;
  margin-bottom: 0;
}
.active_user .user_cred p strong {
  color: #fff;
  display: block;
}
.active_user .user_cred p strong a {
  color: #fff;
  text-decoration: none;
}
.active_user .user_cred p strong a:hover {
  color: #000;
}

.user_stats {
  padding: 10px;
  margin-bottom: 10px;
}
.user_stats h4 {
  font-size: 14px;
  color: #fff;
  text-transform: uppercase;
}
.user_stats .progress {
  border-radius: 2px;
  background-color: rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.48);
  overflow: visible;
}
.user_stats .progress .progress-bar {
  background-color: #fff;
  box-shadow: 0px 0px 4px 1px rgba(255, 255, 255, 0.76);
  border-radius: 2px;
}

.pro_label {
  color: #fff;
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}
.pro_label p {
  margin-bottom: 0;
  font-size: 12px;
  font-weight: 300;
}
.pro_label p i {
  padding-right: 10px;
}

.main_panel {
  min-height: 100%;
  width: 100%;
}
.main_panel .content_wrapper {
  padding: 0.3rem 1.04rem;
  width: 100%;
  -webkit-flex-grow: 1;
  flex-grow: 1;
}

.item_list .custom-checkbox {
  padding-left: 0;
}
.item_list .custom-checkbox .custom-control-input:checked ~ .custom-control-label:before {
  background-color: #b31f24;
  background: -moz-linear-gradient(top, #fa8443 0%, #fa8343 1%, #f84773 100%);
  background: -webkit-linear-gradient(top, #fa8443 0%, #fa8343 1%, #f84773 100%);
  background: linear-gradient(to bottom, #fa8443 0%, #fa8343 1%, #f84773 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#e6fa8443", endColorstr="#e6f84773",GradientType=0 );
}
.item_list .custom-checkbox .custom-control-input:checked ~ .custom-control-label img {
  border: 4px solid #b31f24;
}
.item_list .custom-checkbox .custom-control-label {
  box-shadow: none;
}
.item_list .custom-checkbox .custom-control-label:before {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0;
  box-shadow: none;
  background-color: #fff;
  bottom: 6px;
  top: auto;
  right: 5px;
  left: auto;
}
.item_list .custom-checkbox .custom-control-label:after {
  width: 1.5rem;
  height: 1.5rem;
  bottom: 6px;
  top: auto;
  right: 5px;
  left: auto;
}
.item_list .inner_item_single {
  margin-bottom: 6px;
  cursor: pointer;
  border: 1px solid #bfbfbf;
  background-color: #fff;
}
.item_list .inner_item_single:hover img {
  opacity: 0.8;
}
.item_list .inner_item_single:hover .desc {
  background-color: #fff;
}
.item_list .inner_item_single img {
  height: 120px;
  object-fit: cover;
  width: 100%;
  padding: 5px;
  transition: 0.2s linear;
}
.item_list .desc {
  width: 100%;
  padding: 8px;
  background-color: #f7f9fa;
  border-top: 1px solid #bfbfbf;
  transition: 0.2s linear;
}
.item_list .desc h5 {
  font-size: 12px;
  color: #323335;
  font-weight: 700;
  border-radius: 5px;
  margin-bottom: 5px;
}
.item_list .desc p {
  background-color: #b31f24;
  background: -moz-linear-gradient(top, #fa8443 0%, #fa8343 1%, #f84773 100%);
  background: -webkit-linear-gradient(top, #fa8443 0%, #fa8343 1%, #f84773 100%);
  background: linear-gradient(to bottom, #fa8443 0%, #fa8343 1%, #f84773 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#e6fa8443", endColorstr="#e6f84773",GradientType=0 );
  padding: 4px 10px;
  font-size: 13px;
  color: #fff;
  font-weight: 600;
  border-radius: 5px;
  display: inline;
}

.invoice .top_title {
  background-color: #b31f24;
  padding: 10px;
  text-align: center;
}
.invoice .top_title h5 {
  color: #fff;
  margin-bottom: 0;
}
.invoice .top_title iframe {
  background-color: #fff;
  width: 100%;
  height: 50px;
  padding: 8px;
}

.item_que {
  background-color: #fff;
}
.item_que .table th {
  padding: 5px 0.75rem;
  font-size: 14px;
  font-weight: bold;
}
.item_que .table td {
  color: #2E55F9;
  padding: 5px 0.75rem !important;
  font-weight: 600;
}
.item_que .table td a {
  color: red;
}
.item_que .table td a:hover {
  color: #000;
}

.scroll_2 {
  height: 370px;
  max-width: 100%;
}
.scroll_2.sb-container {
  padding: 0px 15px 0 0;
}
.scroll_2 .sb-scrollbar-container {
  right: 5px;
  width: 6px;
}
.scroll_2 .sb-scrollbar {
  width: 6px;
}

.item_row {
  padding: 15px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.item_row ul {
  display: flex;
  margin-bottom: 0;
}
.item_row ul li a {
  color: #ea2225;
  font-size: 20px;
  padding-right: 5px;
  margin-right: 10px;
}
.item_row ul li a:hover {
  color: #000;
}
.item_row ul li h5 {
  font-size: 14px;
  color: #323335;
  font-weight: 700;
  border-radius: 5px;
  margin-bottom: 5px;
}
.item_row ul li p {
  margin-bottom: 0;
}
.item_row ul li .form-control {
  text-align: center;
}
.item_row ul li .btn {
  box-shadow: none;
  background-color: #b31f24;
  background: -moz-linear-gradient(top, #fa8443 0%, #fa8343 1%, #f84773 100%);
  background: -webkit-linear-gradient(top, #fa8443 0%, #fa8343 1%, #f84773 100%);
  background: linear-gradient(to bottom, #fa8443 0%, #fa8343 1%, #f84773 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#e6fa8443", endColorstr="#e6f84773",GradientType=0 );
  color: #fff;
}

.sub_total {
  padding: 1px 15px 5px;
  background-color: #fff;
}
.sub_total ul {
  margin-bottom: 0;
}
.sub_total ul li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 0;
}
.sub_total ul li:last-child {
  border-top: 1px solid #000;
  padding-top: 8px;
  margin-top: 8px;
}
.sub_total ul li p {
  margin-bottom: 0;
  font-size: 13px;
}
.sub_total ul li h2 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 0;
  color: #1bc007;
}
.sub_total .btn {
  display: block;
  margin-top: 5px;
  width: 100%;
}

.scroll_1 {
  height: 650px;
  max-width: 100%;
}

.sb-container {
  position: relative;
  overflow: hidden;
  padding-right: 10px;
  padding: 0px 30px 0 0;
}

.sb-container td img {
  margin-right: 5px;
}

.sb-container td {
  padding: 14px 8px !important;
}

.sb-content {
  height: 100%;
  width: 120%;
  padding-right: 20%;
  overflow-y: scroll;
  box-sizing: border-box;
}

.sb-scrollbar-container {
  position: absolute;
  right: 25px;
  bottom: 35px;
  top: 30px;
  width: 10px;
  background-color: rgba(0, 0, 0, 0.3);
}

.sb-container-noscroll .sb-scrollbar-container {
  right: -20px;
}

.sb-scrollbar {
  position: absolute;
  right: 0;
  height: 30px;
  width: 10px;
  border-radius: 0;
  background-color: #fa8443;
  background: -moz-linear-gradient(top, #fa8443 0%, #fa8343 1%, #f84773 100%);
  background: -webkit-linear-gradient(top, #fa8443 0%, #fa8343 1%, #f84773 100%);
  background: linear-gradient(to bottom, #fa8443 0%, #fa8343 1%, #f84773 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#e6fa8443", endColorstr="#e6f84773",GradientType=0 );
}

.sb-scrollbar:hover {
  background: rgba(0, 0, 0, 0.5);
}

.table th {
  border-top: 0;
  border-bottom-width: 1px;
  font-weight: 500;
  font-size: 1rem;
}
.table td {
  font-size: 0.875rem;
}
.table td img {
  width: 36px;
  height: 36px;
  border-radius: 100%;
}

.progress {
  border-radius: 3px;
  height: 8px;
}

.grid-margin {
  margin-bottom: 1.875rem;
}

.fix_footer {
  position: fixed;
  width: 100%;
  left: 0;
  background-color: #fff;
  padding: 7px 0;
  z-index: 11;
  bottom: 0;
}
.fix_footer .btn {
  border-radius: 0;
  padding: 15px 25px;
  text-transform: uppercase;
}
.fix_footer .table {
  margin-bottom: 0;
}
.fix_footer .table td {
  width: 50%;
  display: inline-block;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: 45px;
}
.fix_footer .table td p {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  margin-bottom: 0;
}
.fix_footer .table td p span .form-control {
  width: 75px;
  text-align: center;
  font-size: 13px;
  padding: 2px;
}

.open_navigation .sidebar {
  width: 257px;
  position: relative;
  height: auto;
  top: 0;
}
.open_navigation .sidebar .top_brand_area .brand_logo {
  opacity: 1;
  visibility: visible;
  width: auto;
  margin: 0;
}
.open_navigation .sidebar .nav .nav-item .nav-link span {
  margin-left: 0;
  opacity: 1;
  visibility: visible;
  transition: margin-left 0.3s linear, opacity 1s ease, visibility 1s ease;
  width: auto;
}
.open_navigation .sidebar .nav .nav-item .nav-link i.fa.fa-angle-down {
  opacity: 1;
  visibility: visible;
}
.open_navigation .main_panel {
  width: calc(100% - 257px);
  padding-left: 0;
}
/*--------------
	================FORMS
--------------*/
/* Forms */
.forms .form-control {
  border-radius: 0;
  box-shadow: none;
  font-size: 14px;
  border-radius: 2px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  padding: 0.575rem 1.375rem;
}
.forms .form-control:placholder {
  color: #ccc;
}
.forms .form-control:focus {
  border-color: #b31f24;
  box-shadow: none;
}
.forms .form-group {
  margin-bottom: 15px;
}
.forms .form-group label {
  font-size: 0.875rem;
  line-height: 1.4rem;
  vertical-align: top;
  margin-bottom: 0;
  font-weight: 500;
}
.forms .file-upload-default {
  visibility: hidden;
  position: absolute;
}
.forms .file-upload-info {
  background: transparent;
}

.search_form .form-group {
  margin-bottom: 10px;
}
.search_form .form-group label {
  font-size: 13px !important;
  font-weight: 600;
  text-transform: uppercase;
}

.btn {
  font-size: 0.875rem;
  line-height: 1;
  font-weight: 400;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 2px 2px 0 rgba(77, 131, 255, 0.14), 0 3px 1px -2px rgba(77, 131, 255, 0.2), 0 1px 5px 0 rgba(77, 131, 255, 0.12);
}
.btn:hover {
  box-shadow: 0 2px 2px 0 rgba(77, 131, 255, 0.14), 0 3px 1px -2px rgba(77, 131, 255, 0.2), 0 1px 5px 0 rgba(77, 131, 255, 0.12);
}

td .btn {
  padding: 6px 12px;
}

/*---kams--------*/
.kams_visit {
  margin-top: 25px;
}
.kams_visit .form-control {
  border-radius: 0;
  height: 55px;
}
.kams_visit .btn {
  background-color: #b31f24;
  color: #fff;
  border-radius: 25px;
  width: 150px;
  font-size: 16px;
  text-transform: uppercase;
}
.kams_visit .btn:hover {
  background-color: #000;
}

.top_info_area {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.top_info_area h1 {
  color: #b31f24;
  text-transform: uppercase;
  font-size: 32px;
}
.top_info_area h1 small {
  display: block;
  color: #1d1d1d;
  font-size: 18px;
  text-transform: capitalize;
  font-weight: normal;
  margin-top: 10px;
}
.top_info_area .btn {
  background-color: #b31f24;
  color: #fff;
  border-radius: 25px;
  width: 150px;
  font-size: 16px;
  text-transform: uppercase;
}
.top_info_area .btn:hover {
  background-color: #000;
}

.kams_blocks {
  margin: 25px 0;
}
.kams_blocks .inner_kams {
  background-color: #fff;
  box-shadow: 1px 2px 5px 1px rgba(0, 0, 0, 0.05);
  padding: 15px;
  border-radius: 10px;
  height: 100%;
}
.kams_blocks .inner_kams ul {
  margin-bottom: 0;
}
.kams_blocks .inner_kams ul li {
  display: flex;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #ccc;
  font-size: 14px;
}
.kams_blocks .inner_kams ul li:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}
.kams_blocks .inner_kams ul li strong {
  color: #b31f24;
}
.kams_blocks .inner_kams ul li span {
  color: #000000;
}

/*-----------*/
.wizard-content-left {
  background-blend-mode: darken;
  background-color: rgba(0, 0, 0, 0.45);
  background-image: url("https://i.ibb.co/X292hJF/form-wizard-bg-2.jpg");
  background-position: center center;
  background-size: cover;
  height: 100vh;
  padding: 30px;
}

.wizard-content-left h1 {
  color: #ffffff;
  font-size: 38px;
  font-weight: 600;
  padding: 12px 20px;
  text-align: center;
}

.form-wizard {
  color: #888888;
  padding: 30px;
}

.form-wizard .wizard-form-radio {
  display: inline-block;
  margin-left: 5px;
  position: relative;
}

.form-wizard .wizard-form-radio input[type=radio] {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  -o-appearance: none;
  appearance: none;
  background-color: #dddddd;
  height: 25px;
  width: 25px;
  display: inline-block;
  vertical-align: middle;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
}

.form-wizard .wizard-form-radio input[type=radio]:focus {
  outline: 0;
}

.form-wizard .wizard-form-radio input[type=radio]:checked {
  background-color: #fb1647;
}

.form-wizard .wizard-form-radio input[type=radio]:checked::before {
  content: "";
  position: absolute;
  width: 10px;
  height: 10px;
  display: inline-block;
  background-color: #ffffff;
  border-radius: 50%;
  left: 1px;
  right: 0;
  margin: 0 auto;
  top: 8px;
}

.form-wizard .wizard-form-radio input[type=radio]:checked::after {
  content: "";
  display: inline-block;
  webkit-animation: click-radio-wave 0.65s;
  -moz-animation: click-radio-wave 0.65s;
  animation: click-radio-wave 0.65s;
  background: #000000;
  content: "";
  display: block;
  position: relative;
  z-index: 100;
  border-radius: 50%;
}

.form-wizard .wizard-form-radio input[type=radio] ~ label {
  padding-left: 10px;
  cursor: pointer;
}

.form-wizard .form-wizard-header {
  text-align: center;
}

.form-wizard .form-wizard-next-btn, .form-wizard .form-wizard-previous-btn, .form-wizard .form-wizard-submit {
  background-color: #b31f24;
  color: #ffffff;
  display: inline-block;
  min-width: 100px;
  min-width: 120px;
  padding: 10px;
  text-align: center;
  text-decoration: none;
}

.form-wizard .form-wizard-next-btn:hover, .form-wizard .form-wizard-next-btn:focus, .form-wizard .form-wizard-previous-btn:hover, .form-wizard .form-wizard-previous-btn:focus, .form-wizard .form-wizard-submit:hover, .form-wizard .form-wizard-submit:focus {
  color: #ffffff;
  opacity: 0.6;
  text-decoration: none;
}

.form-wizard .form-wizard-next-btn-show, .form-wizard .form-wizard-previous-btn, .form-wizard .form-wizard-submit {
  background-color: #b31f24;
  color: #ffffff;
  display: inline-block;
  min-width: 100px;
  min-width: 120px;
  padding: 10px;
  text-align: center;
  text-decoration: none;
}

.form-wizard .form-wizard-next-btn-show:hover, .form-wizard .form-wizard-next-btn-show:focus, .form-wizard .form-wizard-previous-btn:hover, .form-wizard .form-wizard-previous-btn:focus, .form-wizard .form-wizard-submit:hover, .form-wizard .form-wizard-submit:focus {
  color: #ffffff;
  opacity: 0.6;
  text-decoration: none;
}
.form-wizard .wizard-fieldset {
  display: none;
}

.form-wizard .wizard-fieldset.show {
  display: block;
}

.form-wizard .wizard-form-error {
  display: none;
  background-color: #d70b0b;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  width: 100%;
}

.form-wizard .form-wizard-previous-btn {
  background-color: #fb1647;
}

.form-wizard .form-group {
  position: relative;
  margin: 25px 0;
}

.form-wizard .form-wizard-steps {
  margin: 30px 0;
}

.form-wizard .form-wizard-steps li {
  width: 25%;
  float: left;
  position: relative;
}

.form-wizard .form-wizard-steps li::after {
  background-color: #f3f3f3;
  content: "";
  height: 5px;
  left: 0;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  border-bottom: 1px solid #dddddd;
  border-top: 1px solid #dddddd;
}

.form-wizard .form-wizard-steps li span {
  background-color: #dddddd;
  border-radius: 50%;
  display: inline-block;
  height: 40px;
  line-height: 40px;
  position: relative;
  text-align: center;
  width: 40px;
  z-index: 1;
}

.form-wizard .form-wizard-steps li:last-child::after {
  width: 50%;
}

.form-wizard .form-wizard-steps li.active span, .form-wizard .form-wizard-steps li.activated span {
  background-color: #b31f24;
  color: #ffffff;
}

.form-wizard .form-wizard-steps li.active::after, .form-wizard .form-wizard-steps li.activated::after {
  background-color: #b31f24;
  left: 50%;
  width: 50%;
  border-color: #b31f24;
}

.form-wizard .form-wizard-steps li.activated::after {
  width: 100%;
  border-color: #b31f24;
}

.form-wizard .form-wizard-steps li:last-child::after {
  left: 0;
}

.form-wizard .wizard-password-eye {
  position: absolute;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

@keyframes click-radio-wave {
  0% {
    width: 25px;
    height: 25px;
    opacity: 0.35;
    position: relative;
  }
  100% {
    width: 60px;
    height: 60px;
    margin-left: -15px;
    margin-top: -15px;
    opacity: 0;
  }
}
@media screen and (max-width: 767px) {
  .wizard-content-left {
    height: auto;
  }
}
/*-----------*/
.group_ticket_parent h3 {
  text-transform: uppercase;
  color: #707070;
  font-size: 25px;
  font-weight: 600;
  margin-bottom: 20px;
}
.group_ticket_parent label {
  color: #444444;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 600;
  color: #707070;
  margin-top: 15px;
}
.group_ticket_parent .form-control {
  appearance: auto;
  border-radius: 0;
  height: 50px;
  font-size: 15px;
}
.group_ticket_parent .form-control:focus {
  box-shadow: none;
}
.group_ticket_parent .btn {
  background-color: #b31f24;
  color: #fff;
  width: 220px;
  margin-top: 15px;
  height: 45px;
  text-transform: uppercase;
  font-weight: 600;
}
.group_ticket_parent .btn:hover {
  background-color: #FFC619;
  color: #000;
}

.numOfSeact {
  margin-top: 15px;
}

.journey_detail {
  margin-top: 25px;
}
.journey_detail .selection {
  border: 1px solid #ffc619;
  height: 55px;
  position: relative;
  background-color: #fff;
}
.journey_detail .selection .form-control {
  height: 100%;
  border: 0;
  padding: 2px 10px;
  font-weight: bold;
  font-size: 18px;
  padding-bottom: 15px;
  position: relative;
  z-index: 1;
  background-color: transparent;
}
.journey_detail .selection .form-control::placeholder {
  color: #000;
}
.journey_detail .selection small {
  color: #000;
  position: absolute;
  bottom: 2px;
  left: 12px;
  font-size: 12px;
  white-space: nowrap;
  width: 92%;
  overflow: hidden;
}
.journey_detail .selection.dropdown .dropdown-menu {
  padding: 0;
  border-radius: 0;
}
.journey_detail .selection.dropdown .dropdown-menu .form-control {
  border: 1px solid #ccc;
  font-size: 14px;
  font-weight: normal;
  padding: 0.375rem 0.75rem;
  height: 45px;
  margin-bottom: 15px;
}
.journey_detail .selection.dropdown .dropdown-menu .card {
  border: 0;
}
.journey_detail .selection.dropdown .dropdown-menu .card .form-check label {
  margin: 0;
}

.stop_over {
  padding: 5px 0;
  text-align: center;
  /* background-color: #FCE7E8; */
  margin: 10px 0;
}
.stop_over p {
  margin-bottom: 0;
  color: #b31f24;
  font-size: 13px;
  font-weight: 500;
}

.addRemove_segment {
  display: flex;
  justify-content: flex-end;
}
.addRemove_segment .btn {
  background-color: #b31f24;
  color: #fff;
  margin-left: 10px;
  border-radius: 0;
  border-color: #b31f24;
}
.addRemove_segment .btn:hover {
  background-color: #FFC619;
  color: #000;
  border-color: #FFC619;
}

.fare_summary, .baggage_detail {
  border: 1px solid #DBDBDB;
  padding: 15px;
  box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.16);
  margin-top: 25px;
}

.fare_calculator {
  text-align: center;
  height: 100%;
  background-color: #F3F3F3;
  position: relative;
  height: calc(100% - 15px);
}
.fare_calculator label {
  background-color: #fff;
  display: block;
  width: 100%;
}
.fare_calculator .totalBagge_fare {
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 50%;
  transform: translateY(-50%);
  margin-bottom: 0;
}
.fare_calculator .totalBagge_fare h4 {
  font-weight: 700;
  font-size: 30px;
}
.fare_calculator .totalBagge_fare h4 span {
  display: block;
  font-weight: 300;
  font-size: 16px;
}

/*-----------*/
.footer {
  background-color: #fff;
  text-align: center;
}
.footer p {
  margin-bottom: 0;
  padding: 5px 0;
}

/*--------*/
.header {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
  box-shadow: 1px 2px 5px 1px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
}
.header .nav-item img {
  width: 43px;
  height: 43px;
  object-fit: cover;
  object-position: top;
  border-radius: 50%;
  padding: 2px;
  border: 2px solid #b31f24;
  margin-right: 10px;
}
.header .nav-item.dropdown {
  display: inline-block;
  padding: 6px 12px;
}
.header .nav-item .dropdown-menu {
  margin: 0;
  padding: 0;
  border-radius: 0;
  border: 0;
  box-shadow: 1px 2px 5px 1px rgba(0, 0, 0, 0.16);
  font-size: 13px;
  right: 0;
}
.header .nav-item .dropdown-menu .dropdown-item {
  padding: 8px 15px;
}
.header .nav-item .dropdown-menu .dropdown-item:visited, .header .nav-item .dropdown-menu .dropdown-item:focus, .header .nav-item .dropdown-menu .dropdown-item:active {
  background-color: #b31f24;
}
.header .nav-item .dropdown-menu .dropdown-item:hover {
  background-color: #b31f24;
  color: #fff;
}
.header .nav-item .nav-link {
  color: #000;
}

.statistics {
  background-color: #fff;
  box-shadow: 1px 2px 6px 1px rgba(0, 0, 0, 0.19);
  border-radius: 5px;
  padding: 10px;
}
.statistics h4 {
  display: flex;
  justify-content: space-between;
  opacity: 1;
  visibility: visible;
}
.statistics h4 i {
  background-color: #4b73f6;
  width: 70px;
  height: 70px;
  text-align: center;
  color: #fff;
  font-size: 40px;
  line-height: 70px;
  border-radius: 5px;
  margin-top: -30px;
}
.statistics h3 {
  display: flex;
  font-size: 14px;
  justify-content: space-between;
  align-items: center;
  font-weight: 300;
}
.statistics h3 strong {
  font-size: 30px;
}
.statistics p {
  margin-bottom: 0;
  color: #727272;
}
.statistics p i {
  margin-right: 10px;
  font-size: 25px;
  color: #000;
}
.statistics p strong {
  color: #4B4B4B;
  font-size: 16px;
  margin-left: 9px;
}
.statistics.budget h4 {
  color: #4680ff;
}
.statistics.budget h4 i {
  background-color: #4680ff;
}
.statistics.budget p i {
  color: #4680ff;
}
.statistics.revenue h4 {
  color: #fc6180;
}
.statistics.revenue h4 i {
  background-color: #fc6180;
}
.statistics.revenue p i {
  color: #fc6180;
}
.statistics.profit h4 {
  color: #93be52;
}
.statistics.profit h4 i {
  background-color: #93be52;
}
.statistics.profit p i {
  color: #93be52;
}
.statistics.booking h4 {
  color: #0967be;
}
.statistics.booking h4 i {
  background-color: #0967be;
}
.statistics.booking p i {
  color: #0967be;
}

/*-----------*/
.charts {
  background-color: #fff;
  box-shadow: 1px 2px 6px 1px rgba(0, 0, 0, 0.19);
  border-radius: 5px;
  padding: 10px;
  margin-top: 35px;
}
.charts .title_top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 10px;
}
.charts .title_top h5 {
  font-size: 16px;
  margin-bottom: 0;
}
.charts .title_top ul {
  display: flex;
  margin-bottom: 0;
}
.charts .title_top ul li a {
  color: #616161;
  padding-left: 10px;
}
.charts .title_top ul li a:hover {
  color: #b31f24;
}

.current_status {
  background-color: #fff;
  box-shadow: 1px 2px 6px 1px rgba(0, 0, 0, 0.19);
  border-radius: 5px;
  padding: 15px 25px;
}
.current_status ul {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.current_status ul h4 {
  font-size: 25px;
  text-transform: uppercase;
  text-decoration: underline;
  margin-bottom: 15px;
}
.current_status ul li {
  display: flex;
  align-items: center;
  font-weight: 300;
  color: #8e8ea1;
  padding: 1px 0;
}
.current_status ul li i {
  margin-right: 10px;
  font-size: 18px;
  color: #e72b41;
}
.current_status ul li.active {
  font-weight: 500;
  color: #393939;
}
.current_status ul li.active i {
  color: #5bb017;
}
.current_status ul:nth-child(1) h4 {
  color: #f3af9c;
}
.current_status ul:nth-child(2) h4 {
  color: #00ff00;
}
.current_status ul:nth-child(3) h4 {
  color: #f0e561;
}
.current_status ul:last-child {
  border-bottom: 0;
  margin-bottom: 0;
  padding-bottom: 0;
}

.box {
  background-color: #fff;
  box-shadow: 1px 2px 6px 1px rgba(0, 0, 0, 0.19);
  border-radius: 5px;
  padding: 10px;
  margin-top: 35px;
}
.box td span {
  display: block;
}

.heading {
  background-color: #ef5350;
  padding: 10px;
  margin-bottom: 15px;
}
.heading h3 {
  margin-bottom: 0;
  font-size: 20px;
  color: #fff;
}

/*--------------------*/
@media (min-width: 992px) {
  .animate {
    animation-duration: 0.3s;
    -webkit-animation-duration: 0.3s;
    animation-fill-mode: both;
    -webkit-animation-fill-mode: both;
  }
}
@keyframes slideIn {
  0% {
    transform: translateY(1rem);
    opacity: 0;
  }
  100% {
    transform: translateY(0rem);
    opacity: 1;
  }
  0% {
    transform: translateY(1rem);
    opacity: 0;
  }
}
@-webkit-keyframes slideIn {
  0% {
    -webkit-transform: transform;
    -webkit-opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0);
    -webkit-opacity: 1;
  }
  0% {
    -webkit-transform: translateY(1rem);
    -webkit-opacity: 0;
  }
}
.slideIn {
  -webkit-animation-name: slideIn;
  animation-name: slideIn;
}

.nav-item.dropdown:hover .dropdown-menu {
  display: block;
}

/*------------------------*/
@media screen and (max-width: 1024px) {
  .sidebar {
    width: 220px;
    position: absolute;
    left: 0;
    z-index: 1;
  }

  .main_panel {
    padding-left: 50px;
  }
  .main_panel .content_wrapper {
    padding: 0;
  }

  .sb-container {
    padding-right: 15px;
  }

  .sb-scrollbar-container {
    right: 0;
    width: 4px;
  }

  .sb-scrollbar {
    width: 4px;
  }
}
@media screen and (max-width: 767px) {
  .statistics {
    margin-bottom: 50px;
  }

  .header .nav-item.dropdown {
    padding: 0;
  }
}
@media screen and (max-width: 580px) {
  .fixed-top {
    background-color: #fff;
  }

  .navbar .navbar-brand-wrapper {
    border-right: 0;
    width: auto;
  }
  .navbar .navbar-brand-wrapper .navbar-brand {
    margin-right: 15px;
  }
  .navbar .navbar-brand-wrapper .navbar-brand img {
    width: 60px;
  }

  .navbar-menu-wrapper {
    padding-right: 0;
  }
  .navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.nav-profile {
    margin-right: 0;
    margin-left: 10px;
  }
  .navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item {
    margin: 0 5px !important;
  }
  .navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item .nav-link.mobile_search {
    display: block;
  }
  .navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item i {
    font-size: 4vw !important;
  }
  .navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.nav-profile .nav-link .nav-profile-name {
    font-size: 3vw;
    margin: 0;
  }
  .navbar-menu-wrapper .searching {
    position: absolute;
    z-index: 1;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 90% !important;
    bottom: -52px;
    display: none;
  }
}
@media screen and (max-width: 520px) {
  .inner_form {
    width: 100%;
    padding: 50px 20px 30px;
  }

  .navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown {
    right: -100px;
    top: 45px;
  }
  .navbar-menu-wrapper .navbar-nav.navbar-nav-right .nav-item.dropdown .navbar-dropdown[aria-labelledby=profileDropdown] {
    right: 0;
  }
}
@media screen and (max-width: 420px) {
  .item_list .custom-checkbox img {
    height: 130px;
  }
  .item_list .desc {
    margin-bottom: 10px;
  }
  .item_list .desc h5 {
    font-size: 3.5vw;
  }
}

/*# sourceMappingURL=style.css.map */
