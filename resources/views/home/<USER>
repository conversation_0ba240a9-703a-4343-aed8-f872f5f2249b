@extends('adminpanel.header')

@section('content')
    <h1>{{ $provider }} User Dashboard</h1>

    <div class="card-wrapper">
        <!-- Conditionally render the provider card based on the provider -->
        @if ($provider === 'Jazz')
            <a href="{{ route('report.data', ['operator' => 'jazz']) }}" class="card-link">
                <div class="card border-danger">
                    <div class="card-body text-center">
                        @php
                            $extensions = ['png', 'jpg']; // Add more if needed
                            $logoPath = null;
                            foreach ($extensions as $ext) {
                                if (file_exists(public_path('assets/logos/jazz.' . $ext))) {
                                    $logoPath = asset('assets/logos/jazz.' . $ext);
                                    break;
                                }
                            }
                        @endphp
                        @if ($logoPath)
                            <img src="{{ $logoPath }}" alt="Jazz Logo">
                        @else
                            <p>Logo not found</p>
                        @endif
                        <p>Jazz</p>
                    </div>
                </div>
            </a>
            <a href="{{ route('report.zong') }}" class="card-link">
                <div class="card border-success">
                    <div class="card-body text-center">
                        @php
                            $extensions = ['png', 'jpg']; // Add more if needed
                            $logoPath = null;
                            foreach ($extensions as $ext) {
                                if (file_exists(public_path('assets/logos/zong.' . $ext))) {
                                    $logoPath = asset('assets/logos/zong.' . $ext);
                                    break;
                                }
                            }
                        @endphp
                        @if ($logoPath)
                            <img src="{{ $logoPath }}" alt="Zong Logo">
                        @else
                            <p>Logo not found</p>
                        @endif
                        <p>Zong</p>
                    </div>
                </div>
            </a>
            <a href="{{ route('report.data', ['operator' => 'ufone']) }}" class="card-link">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        @php
                            $extensions = ['png', 'jpg']; // Add more if needed
                            $logoPath = null;
                            foreach ($extensions as $ext) {
                                if (file_exists(public_path('assets/logos/ufone.' . $ext))) {
                                    $logoPath = asset('assets/logos/ufone.' . $ext);
                                    break;
                                }
                            }
                        @endphp
                        @if ($logoPath)
                            <img src="{{ $logoPath }}" alt="Ufone Logo">
                        @else
                            <p>Logo not found</p>
                        @endif
                        <p>Ufone</p>
                    </div>
                </div>
            </a>
        @elseif ($provider === 'Zong')
            <a href="{{ route('report.zong') }}" class="card-link">
                <div class="card border-success">
                    <div class="card-body text-center">
                        @php
                            $extensions = ['png', 'jpg']; // Add more if needed
                            $logoPath = null;
                            foreach ($extensions as $ext) {
                                if (file_exists(public_path('assets/logos/zong.' . $ext))) {
                                    $logoPath = asset('assets/logos/zong.' . $ext);
                                    break;
                                }
                            }
                        @endphp
                        @if ($logoPath)
                            <img src="{{ $logoPath }}" alt="Zong Logo">
                        @else
                            <p>Logo not found</p>
                        @endif
                        <p>Zong</p>
                    </div>
                </div>
            </a>
        @elseif ($provider === 'Ufone')
            <a href="{{ route('report.data', ['operator' => 'ufone']) }}" class="card-link">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        @php
                            $extensions = ['png', 'jpg']; // Add more if needed
                            $logoPath = null;
                            foreach ($extensions as $ext) {
                                if (file_exists(public_path('assets/logos/ufone.' . $ext))) {
                                    $logoPath = asset('assets/logos/ufone.' . $ext);
                                    break;
                                }
                            }
                        @endphp
                        @if ($logoPath)
                            <img src="{{ $logoPath }}" alt="Ufone Logo">
                        @else
                            <p>Logo not found</p>
                        @endif
                        <p>Ufone</p>
                    </div>
                </div>
            </a>
        @else
            <p>No provider found.</p>
        @endif
    </div>
    

    @include('adminpanel.footer')
@endsection

<style>
    /* Container for the card */
    .card-wrapper {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        /* Aligns items to the top */
        min-height: 100vh;
        /* Takes the full height of the screen */
    }

    /* Link around the card */
    .card-link {
        text-decoration: none;
        /* Removes the underline from the link */
    }

    /* Individual card styles */
    .card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        width: 200px;
        /* Fixed width for each card */
        text-align: center;
        transition: transform 0.3s, box-shadow 0.3s;
        cursor: pointer;
        /* Makes it look clickable */
        margin-top: 20px;
        /* Adds a small gap between the top */
    }

    /* Image inside cards */
    .card img {
        width: 80%;
        max-width: 100px;
        height: auto;
        margin-bottom: 10px;
    }

    /* Hover effects for cards */
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Card title (provider name) */
    .card p {
        margin: 0;
        font-size: 16px;
        font-weight: bold;
    }
</style>
