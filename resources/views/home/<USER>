@extends('adminpanel.header')

@section('content')
    <h1>Admin Dashboard</h1>
    <div class="providers">
        <div class="card-wrapper">
            <a href="{{ route('report.zong') }}" class="card-link"> <!-- Link to dedicated Zong report -->
                <div class="card border-success">
                    <div class="card-body text-center">
                        <img src="{{ asset('assets/logos/zong.jpg') }}" alt="Zong Logo">
                        <p>Zong</p>
                    </div>
                </div>
            </a>
            <a href="{{ route('report.data', ['operator' => 'jazz']) }}" class="card-link"> <!-- Pass operator jazz -->
                <div class="card border-danger">
                    <div class="card-body text-center">
                        <img src="{{ asset('assets/logos/jazz.png') }}" alt="Jazz Logo">
                        <p>Jazz</p>
                    </div>
                </div>
            </a>
            <a href="{{ route('report.data', ['operator' => 'ufone']) }}" class="card-link"> <!-- Pass operator ufone -->
                <div class="card border-warning">
                    <div class="card-body text-center">
                        <img src="{{ asset('assets/logos/ufone.png') }}" alt="Ufone Logo">
                        <p>Ufone</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
@endsection

<style>
    .providers {
        display: flex;
        flex-direction: column;
        gap: 20px;
        align-items: center;
        padding: 20px;
    }

  

    /* Wrapper for multiple cards */
    .card-wrapper {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap; /* Ensures wrapping on small screens */
        max-width: 100%;
    }

    /* Shared card styles */
    .card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        background-color: #f9f9f9;
        text-align: center;
        transition: transform 0.3s, box-shadow 0.3s;
        flex: 1 1 calc(33.333% - 20px);
        max-width: 200px;
        box-sizing: border-box;
    }

    .card img {
        width: 80%;
        max-width: 100px;
        height: auto;
        margin-bottom: 10px;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .card p {
        margin: 0;
        font-size: 16px;
        font-weight: bold;
    }

    /* Style for making the card links work */
    .card-link {
        text-decoration: none; /* Removes default link underline */
        color: inherit; /* Ensures text color is same as parent */
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .single-card .card, .card-wrapper .card {
            flex: 1 1 calc(50% - 20px); 
            max-width: 45%;
        }
    }

    @media (max-width: 768px) {
        .single-card .card, .card-wrapper .card {
            flex: 1 1 100%; 
            max-width: 100%;
        }
    }
</style>
