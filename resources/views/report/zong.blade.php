@extends('adminpanel.header')

<!-- Styles -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">


<style>
    #laravel_datatable_filter {
        display: block !important;
    }

    .table th {
        font-size: 15px;
        text-align: center;
    }

    table.dataTable tfoot th,
    table.dataTable tfoot td {
        padding: 0px 0px 10px 0px;
    }

    #border {
        border-bottom: 1px solid black;
    }

    div.dt-buttons {
        float: none;
    }

    .dt-buttons {
        margin-left: auto;
    }

    .loader {
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        z-index: 9999;
        transform: translate(-50%, -50%);
        background: white;
        padding: 10px 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
    }

    /* Custom styling for grouped rows */
    .group-header td {
        background-color: #e9ecef !important;
        font-weight: bold !important;
        font-size: 14px !important;
        color: #495057 !important;
        border-top: 2px solid #007bff !important;
    }

    .group-header td:hover {
        background-color: #dee2e6 !important;
    }

    /* Add some spacing between groups */
    tr.group + tr td {
        border-top: 2px solid #007bff;
    }
</style>

@section('content')
<!-- Loader -->
<!-- <div class="loader" id="loader">
    <p style="color:rgb(35, 178, 214);">Processing......</p>
</div> -->

<!-- Main Content Wrapper -->
<div id="main-page-div">
<div class="loader" id="loader">
    <p style="color:rgb(35, 178, 214);">Processing......</p>
</div>
    <!-- Date Range Filter-->
    <div class="card mb-3 px-3 py-2 shadow-sm rounded-3 border-0 bg-light">
        <div class="row g-2 align-items-end">
            <div class="col-auto">
                <h4 class="mb-1">Filter by Date</h4>
                <div class="input-group">
                    <input type="text" id="datefilter" class="form-control border-secondary rounded-end"
                           autocomplete="off" placeholder="All Dates" style="width: 215px;" />
                    <input type="hidden" id="fromdate" />
                    <input type="hidden" id="todate" />
                </div>
            </div>
            <div class="col-auto">
                <button id="resetButton" class="btn btn-sm btn-outline-secondary">Reset</button>
            </div>
        </div>

        <h3 class="mb-4 mt-3">Zong Data</h3>

        <!-- Data Table -->
        <div class="table-responsive mt-3">
            <table id="laravel_datatable" class="table table-striped hover" width="100%" cellspacing="0">
                <thead class="text-center">
                    <tr class="text-center">
                        <th>Report Date</th>
                        <th>Channel</th>
                        <th>Total Users Base</th>
                        <th>Charged Base</th>
                        <th>Uncharged Base</th>
                        <th>New Adds</th>
                        <th>Rejoins</th>
                        <th>Gross Adds</th>
                        <th>Unsubs</th>
                        <th>Purged on Charging</th>
                        <th>Total Churn</th>
                        <th>Net Adds</th>
                    </tr>
                </thead>
                <tbody class="text-center"></tbody>
            </table>
        </div>
    </div>
</div>

@include('adminpanel.footer')

<!-- Scripts -->
<script src="//code.jquery.com/jquery-3.5.1.js"></script>
<script src="//cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="//cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="//cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>

<!-- Date Range Picker -->
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<script>
    $(document).ready(function () {
        $('#datefilter').daterangepicker({
            autoUpdateInput: false,
            locale: { cancelLabel: 'Clear' }
        });

        $('#datefilter').on('apply.daterangepicker', function (ev, picker) {
            $(this).val(picker.startDate.format('YYYY-MM-DD') + ' to ' + picker.endDate.format('YYYY-MM-DD'));
            $('#fromdate').val(picker.startDate.format('YYYY-MM-DD'));
            $('#todate').val(picker.endDate.format('YYYY-MM-DD'));
            $('#laravel_datatable').DataTable().ajax.reload();
        });

        $('#datefilter').on('cancel.daterangepicker', function () {
            $(this).val('');
            $('#fromdate').val('');
            $('#todate').val('');
            $('#laravel_datatable').DataTable().ajax.reload();
        });

        $('#resetButton').on('click', function (e) {
            e.preventDefault();
            $('#datefilter').val('');
            $('#fromdate').val('');
            $('#todate').val('');
            $('#laravel_datatable').DataTable().ajax.reload();
        });

        var table = $('#laravel_datatable').DataTable({
            aLengthMenu: [
                [10, 25, 50, 100, 200, -1],
                [10, 25, 50, 100, 200, "All"]
            ],
            "order": [],
            iDisplayLength: 25,
            serverSide: false,
            processing: true,
            ajax: {
                url: "{{ route('report.zong') }}",
                type: 'GET',
                beforeSend: function () {
                    $('#loader').show();
                    $('#main-page-div').hide();  // Hide main content
                    $('.footer').hide();
                },
                complete: function () {
                    $('#loader').hide();
                    $('#main-page-div').show();  // Show main content
                    $('.footer').show();
                },
                data: function (d) {
                    d.fromdate = $('#fromdate').val();
                    d.todate = $('#todate').val();
                    d.group_id = $('#group_id').val();
                }
            },
            dom: '<"d-flex justify-content-between"<l><B>>frtip',
            buttons: [
                {
                    extend: 'excelHtml5',
                    title: 'Report',
                    text: '<i class="fa fa-file-excel-o"></i> Excel',
                },
                {
                    extend: 'csvHtml5',
                    title: 'Report',
                    text: '<i class="fa fa-file-csv-o"></i> CSV',
                },
                {
                    extend: 'pdfHtml5',
                    title: 'Report',
                    orientation: 'landscape',
                    pageSize: 'LEGAL',
                    text: '<i class="fa fa-file-pdf-o"></i> PDF',
                    titleAttr: 'PDF'
                }
            ],
            columns: [
                { data: 'formatted_date', name: 'formatted_date', orderable: false, searchable: false },
                { data: 'channel', name: 'channel', render: nullDataCheck },
                { data: 'total_users_base', name: 'total_users_base', render: nullDataCheck },
                { data: 'charged_base', name: 'charged_base', render: nullDataCheck },
                { data: 'uncharged_base', name: 'uncharged_base', render: nullDataCheck },
                { data: 'new_adds', name: 'new_adds', render: nullDataCheck },
                { data: 'rejoins', name: 'rejoins', render: nullDataCheck },
                { data: 'gross_adds', name: 'gross_adds', render: nullDataCheck },
                { data: 'unsubs', name: 'unsubs', render: nullDataCheck },
                { data: 'purged_on_charging', name: 'purged_on_charging', render: nullDataCheck },
                { data: 'total_churn', name: 'total_churn', render: nullDataCheck },
                { data: 'net_adds', name: 'net_adds', render: nullDataCheck }
            ]
        });

        function nullDataCheck(data) {
            return data === null || data === "" ? '-' : data;
        }
    });
</script>

@endsection
