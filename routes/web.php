<?php

use App\Http\Controllers\LoginController;
use App\Http\Controllers\serverProviderController;
use App\Http\Controllers\reportController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


Route::middleware(['auth'])->group(function () {
    Route::get('/home', [ServerProviderController::class, 'index'])->name('home');
});

// Login and logout routes
Route::get('/', function () {
    return view('login');
})->name('login');

Route::post('/login', [LoginController::class, 'login'])->name('login');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');



Route::get('/data',[reportController::class, 'getFirst'])->name('report.data');
Route::get('/data/zong',[reportController::class, 'getZongReport'])->name('report.zong');

// Route::get('/first-user', [reportController::class, 'getFirst'])->name('report.first-user');